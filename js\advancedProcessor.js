// ===== معالج متقدم للوظائف الإضافية =====

class AdvancedProcessor {
    constructor(fileProcessor) {
        this.fileProcessor = fileProcessor;
    }

    // عرض نتائج HTML
    displayHtmlResults(results) {
        const resultsContent = document.getElementById('resultsContent');
        
        const successCount = results.filter(r => r.success).length;

        const tableHtml = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                تم تحويل ${successCount} ملف إلى HTML بنجاح
            </div>
            
            <div class="table-responsive">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map((result, index) => `
                            <tr>
                                <td><strong>${result.filename}</strong></td>
                                <td>
                                    ${result.success ? 
                                        '<span class="badge badge-success">نجح</span>' : 
                                        '<span class="badge badge-danger">فشل</span>'
                                    }
                                </td>
                                <td>
                                    ${result.success ? 
                                        `<button class="btn btn-sm btn-primary" onclick="advancedProcessor.downloadHtmlFile(${index})">
                                            <i class="bi bi-download"></i> تحميل HTML
                                        </button>
                                        <button class="btn btn-sm btn-secondary" onclick="advancedProcessor.previewHtml(${index})">
                                            <i class="bi bi-eye"></i> معاينة
                                        </button>` : 
                                        `<span class="text-danger">${result.error}</span>`
                                    }
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        resultsContent.innerHTML = tableHtml;
        this.htmlResults = results;
        this.fileProcessor.showResultsSection();
    }

    // تحميل ملف HTML
    downloadHtmlFile(index) {
        const result = this.htmlResults[index];
        if (result && result.success && result.htmlContent) {
            Utils.downloadFile(result.htmlContent, result.filename, 'text/html');
        }
    }

    // معاينة HTML
    previewHtml(index) {
        const result = this.htmlResults[index];
        if (result && result.success && result.htmlContent) {
            const newWindow = window.open('', '_blank');
            newWindow.document.write(result.htmlContent);
            newWindow.document.close();
        }
    }

    // إضافة مقدمة وخاتمة
    async addPrefixSuffix(options) {
        await this.fileProcessor.readFiles();
        
        const {
            prefix = '',
            suffix = '',
            skipEmptyLines = true,
            addLineNumbers = false
        } = options;

        const results = [];

        for (const fileObj of this.fileProcessor.files) {
            try {
                const lines = fileObj.content.split('\n');
                const processedLines = [];
                let lineCounter = 1;

                for (const line of lines) {
                    if (skipEmptyLines && line.trim() === '') {
                        processedLines.push(line);
                        continue;
                    }

                    let processedLine = line;
                    
                    if (addLineNumbers) {
                        processedLine = `${lineCounter}. ${processedLine}`;
                        lineCounter++;
                    }
                    
                    if (prefix) {
                        processedLine = prefix + processedLine;
                    }
                    
                    if (suffix) {
                        processedLine = processedLine + suffix;
                    }

                    processedLines.push(processedLine);
                }

                const newContent = processedLines.join('\n');

                results.push({
                    filename: fileObj.name,
                    success: true,
                    newContent: newContent,
                    processedLines: processedLines.length
                });

            } catch (error) {
                results.push({
                    filename: fileObj.name,
                    success: false,
                    error: error.message
                });
            }
        }

        this.displayPrefixSuffixResults(results);
    }

    // عرض نتائج المقدمة والخاتمة
    displayPrefixSuffixResults(results) {
        const resultsContent = document.getElementById('resultsContent');
        
        const totalProcessed = results.reduce((sum, r) => sum + (r.processedLines || 0), 0);

        const tableHtml = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                تم معالجة ${Utils.formatNumber(totalProcessed)} سطر في ${results.filter(r => r.success).length} ملف
            </div>
            
            <div class="table-responsive">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>الأسطر المعالجة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map((result, index) => `
                            <tr>
                                <td><strong>${result.filename}</strong></td>
                                <td>${result.success ? Utils.formatNumber(result.processedLines) : '-'}</td>
                                <td>
                                    ${result.success ? 
                                        '<span class="badge badge-success">نجح</span>' : 
                                        '<span class="badge badge-danger">فشل</span>'
                                    }
                                </td>
                                <td>
                                    ${result.success ? 
                                        `<button class="btn btn-sm btn-primary" onclick="fileProcessor.downloadProcessedFile(${index})">
                                            <i class="bi bi-download"></i> تحميل
                                        </button>` : 
                                        `<span class="text-danger">${result.error}</span>`
                                    }
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        resultsContent.innerHTML = tableHtml;
        this.fileProcessor.processedResults = results;
        this.fileProcessor.showResultsSection();
    }

    // معالجة الأسطر الفارغة
    async processEmptyLines(options) {
        await this.fileProcessor.readFiles();
        
        const {
            operation = 'remove',
            emptyLineCount = 1,
            preserveStructure = true
        } = options;

        const results = [];

        for (const fileObj of this.fileProcessor.files) {
            try {
                let lines = fileObj.content.split('\n');
                let newContent = '';

                switch (operation) {
                    case 'remove':
                        lines = lines.filter(line => line.trim() !== '');
                        break;
                    case 'removeExtra':
                        lines = this.removeExtraEmptyLines(lines);
                        break;
                    case 'add':
                        lines = this.addEmptyLinesBetween(lines);
                        break;
                    case 'addAfterParagraph':
                        lines = this.addEmptyLinesAfterParagraphs(lines);
                        break;
                    case 'normalize':
                        lines = this.normalizeEmptyLines(lines, emptyLineCount);
                        break;
                }

                newContent = lines.join('\n');

                results.push({
                    filename: fileObj.name,
                    success: true,
                    newContent: newContent,
                    operation: operation
                });

            } catch (error) {
                results.push({
                    filename: fileObj.name,
                    success: false,
                    error: error.message
                });
            }
        }

        this.displayEmptyLinesResults(results);
    }

    // إزالة الأسطر الفارغة الزائدة
    removeExtraEmptyLines(lines) {
        const result = [];
        let lastWasEmpty = false;

        for (const line of lines) {
            if (line.trim() === '') {
                if (!lastWasEmpty) {
                    result.push(line);
                    lastWasEmpty = true;
                }
            } else {
                result.push(line);
                lastWasEmpty = false;
            }
        }

        return result;
    }

    // إضافة أسطر فارغة بين الأسطر
    addEmptyLinesBetween(lines) {
        const result = [];
        
        for (let i = 0; i < lines.length; i++) {
            result.push(lines[i]);
            if (i < lines.length - 1 && lines[i].trim() !== '') {
                result.push('');
            }
        }

        return result;
    }

    // إضافة أسطر فارغة بعد الفقرات
    addEmptyLinesAfterParagraphs(lines) {
        const result = [];
        
        for (let i = 0; i < lines.length; i++) {
            result.push(lines[i]);
            
            // إذا كان السطر الحالي غير فارغ والسطر التالي غير فارغ أيضاً
            if (lines[i].trim() !== '' && 
                i < lines.length - 1 && 
                lines[i + 1].trim() !== '') {
                result.push('');
            }
        }

        return result;
    }

    // توحيد الأسطر الفارغة
    normalizeEmptyLines(lines, count) {
        const result = [];
        let emptyCount = 0;

        for (const line of lines) {
            if (line.trim() === '') {
                emptyCount++;
                if (emptyCount <= count) {
                    result.push(line);
                }
            } else {
                emptyCount = 0;
                result.push(line);
            }
        }

        return result;
    }

    // عرض نتائج معالجة الأسطر الفارغة
    displayEmptyLinesResults(results) {
        const resultsContent = document.getElementById('resultsContent');
        
        const successCount = results.filter(r => r.success).length;

        const tableHtml = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                تم معالجة الأسطر الفارغة في ${successCount} ملف
            </div>
            
            <div class="table-responsive">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>العملية</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map((result, index) => `
                            <tr>
                                <td><strong>${result.filename}</strong></td>
                                <td>${this.getOperationName(result.operation)}</td>
                                <td>
                                    ${result.success ? 
                                        '<span class="badge badge-success">نجح</span>' : 
                                        '<span class="badge badge-danger">فشل</span>'
                                    }
                                </td>
                                <td>
                                    ${result.success ? 
                                        `<button class="btn btn-sm btn-primary" onclick="fileProcessor.downloadProcessedFile(${index})">
                                            <i class="bi bi-download"></i> تحميل
                                        </button>` : 
                                        `<span class="text-danger">${result.error}</span>`
                                    }
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        resultsContent.innerHTML = tableHtml;
        this.fileProcessor.processedResults = results;
        this.fileProcessor.showResultsSection();
    }

    // الحصول على اسم العملية
    getOperationName(operation) {
        const operations = {
            'remove': 'إزالة جميع الأسطر الفارغة',
            'removeExtra': 'إزالة الأسطر الفارغة الزائدة',
            'add': 'إضافة أسطر فارغة',
            'addAfterParagraph': 'إضافة أسطر بعد الفقرات',
            'normalize': 'توحيد الأسطر الفارغة'
        };
        return operations[operation] || operation;
    }

    // إعادة تسمية الملفات
    async renameFiles(options) {
        await this.fileProcessor.readFiles();
        this.fileProcessor.showProgressSection();
        
        const {
            method = 'ai',
            nameLength = 'medium',
            namingStyle = 'descriptive',
            namingPattern = '',
            newNames = '',
            removeSpecialChars = true
        } = options;

        try {
            const results = [];
            
            for (let i = 0; i < this.fileProcessor.files.length; i++) {
                const fileObj = this.fileProcessor.files[i];
                Utils.updateProgress((i / this.fileProcessor.files.length) * 100, `إعادة تسمية الملف ${i + 1} من ${this.fileProcessor.files.length}`);
                
                try {
                    let newName = '';
                    
                    switch (method) {
                        case 'manual':
                            newName = this.getManualName(newNames, i);
                            break;
                        case 'ai':
                            newName = await this.fileProcessor.aiIntegration.generateFilename(
                                fileObj.content, 
                                this.getLengthLimit(nameLength)
                            );
                            break;
                        case 'pattern':
                            newName = this.generatePatternName(namingPattern, i);
                            break;
                        case 'content':
                            newName = this.generateContentBasedName(fileObj.content, nameLength);
                            break;
                    }

                    if (removeSpecialChars) {
                        newName = this.cleanFileName(newName);
                    }

                    if (!newName.endsWith('.txt')) {
                        newName += '.txt';
                    }

                    results.push({
                        originalName: fileObj.name,
                        newName: newName,
                        success: true
                    });

                } catch (error) {
                    results.push({
                        originalName: fileObj.name,
                        newName: fileObj.name,
                        success: false,
                        error: error.message
                    });
                }
                
                if (i < this.fileProcessor.files.length - 1) {
                    await Utils.delay(1000);
                }
            }
            
            this.displayRenameResults(results);
            
        } catch (error) {
            Utils.showAlert('فشل في إعادة التسمية: ' + error.message, 'danger');
        } finally {
            this.fileProcessor.hideProgressSection();
        }
    }

    // الحصول على اسم يدوي
    getManualName(newNames, index) {
        const names = newNames.split('\n').filter(name => name.trim().length > 0);
        return names[index] || `ملف_${index + 1}`;
    }

    // الحصول على حد الطول
    getLengthLimit(nameLength) {
        const limits = {
            'short': 30,
            'medium': 50,
            'long': 80
        };
        return limits[nameLength] || 50;
    }

    // إنشاء اسم بناءً على النمط
    generatePatternName(pattern, index) {
        const now = new Date();
        return pattern
            .replace('{رقم}', (index + 1).toString().padStart(3, '0'))
            .replace('{تاريخ}', now.toISOString().split('T')[0])
            .replace('{وقت}', now.toTimeString().split(' ')[0].replace(/:/g, '-'));
    }

    // إنشاء اسم بناءً على المحتوى
    generateContentBasedName(content, nameLength) {
        const words = Utils.textToWords(content);
        const limit = this.getLengthLimit(nameLength);
        
        let name = words.slice(0, 8).join('_');
        if (name.length > limit) {
            name = name.substring(0, limit);
        }
        
        return name || `محتوى_${Date.now()}`;
    }

    // تنظيف اسم الملف
    cleanFileName(name) {
        return name
            .replace(/[^\u0600-\u06FFa-zA-Z0-9\s_-]/g, '')
            .replace(/\s+/g, '_')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');
    }

    // عرض نتائج إعادة التسمية
    displayRenameResults(results) {
        const resultsContent = document.getElementById('resultsContent');
        
        const successCount = results.filter(r => r.success).length;

        const tableHtml = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                تم إعادة تسمية ${successCount} ملف بنجاح
            </div>
            
            <div class="table-responsive">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>الاسم الأصلي</th>
                            <th>الاسم الجديد</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map(result => `
                            <tr>
                                <td><strong>${result.originalName}</strong></td>
                                <td>${result.newName}</td>
                                <td>
                                    ${result.success ? 
                                        '<span class="badge badge-success">نجح</span>' : 
                                        '<span class="badge badge-danger">فشل</span>'
                                    }
                                    ${!result.success ? `<br><small class="text-danger">${result.error}</small>` : ''}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        resultsContent.innerHTML = tableHtml;
        this.renameResults = results;
        this.fileProcessor.showResultsSection();
    }
}

// إنشاء مثيل عام من المعالج المتقدم
const advancedProcessor = new AdvancedProcessor(fileProcessor);
