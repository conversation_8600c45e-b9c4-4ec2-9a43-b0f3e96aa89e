# أداة معالجة ملفات النصوص المتقدمة

أداة احترافية شاملة لمعالجة وتحليل ملفات النصوص باستخدام الذكاء الصناعي مع واجهة عربية متطورة.

## المميزات الرئيسية

### 🔍 التحليل والإحصائيات
- تحليل تفصيلي للملفات بالذكاء الصناعي
- حساب عدد الأسطر والكلمات والأحرف
- إحصائيات متقدمة ومؤشرات الأداء
- تحديد نوع المحتوى واللغة المستخدمة

### ✏️ المعالجة والتحرير
- دمج ملفات متعددة في ملف واحد
- استبدال وحذف النصوص بطرق متقدمة
- إضافة محتوى في مواضع محددة
- إضافة مقدمة وخاتمة للأسطر
- معالجة الأسطر الفارغة

### 🎨 التحويل والتنسيق
- تحويل إلى صفحات HTML مميزة
- قوالب تصميم متعددة وألوان متناسقة
- تصميم متجاوب يدعم جميع الأجهزة
- دعم كامل للغة العربية (RTL)

### 🤖 الذكاء الصناعي
- تكامل مع Gemini API مع تدوير المفاتيح
- تحليل ذكي للمحتوى والموضوعات
- إنشاء أسماء ملفات ذكية
- تحسين النصوص وتصحيح الأخطاء

### 📊 التصدير والمشاركة
- تصدير بصيغ متعددة (HTML, Excel, PDF, ZIP)
- تقارير تفاعلية مع محرك بحث
- إعدادات قابلة للتصدير والاستيراد
- معاينة فورية للنتائج

## التقنيات المستخدمة

- **HTML5** - هيكل الصفحة الأساسي
- **CSS3** - التصميم والتنسيق المتقدم
- **Bootstrap 5** - إطار العمل للتصميم المتجاوب
- **JavaScript ES6+** - المنطق والوظائف التفاعلية
- **Gemini AI API** - الذكاء الصناعي والتحليل
- **jsPDF** - إنشاء ملفات PDF
- **JSZip** - إنشاء الملفات المضغوطة

## هيكل المشروع

```
txt-counter/
├── index.html              # الصفحة الرئيسية
├── css/
│   ├── styles.css          # التصميم الأساسي
│   └── components.css      # تصميم المكونات
├── js/
│   ├── main.js            # الملف الرئيسي للتطبيق
│   ├── utils.js           # الوظائف المساعدة
│   ├── aiIntegration.js   # تكامل الذكاء الصناعي
│   ├── fileProcessor.js   # معالجة الملفات
│   ├── advancedProcessor.js # المعالجة المتقدمة
│   ├── advancedForms.js   # النماذج المتقدمة
│   └── exportUtils.js     # أدوات التصدير
└── README.md              # هذا الملف
```

## طريقة الاستخدام

### 1. رفع الملفات
- اسحب وأفلت ملفات TXT في المنطقة المخصصة
- أو انقر لاختيار الملفات من جهازك
- يدعم رفع ملفات متعددة في نفس الوقت

### 2. اختيار العملية
اختر من العمليات المتاحة:
- **تحليل تفصيلي**: تحليل شامل بالذكاء الصناعي
- **عد الأسطر**: إحصائيات مفصلة للملفات
- **دمج الملفات**: دمج جميع الملفات في ملف واحد
- **استبدال النصوص**: البحث والاستبدال المتقدم
- **إضافة محتوى**: إضافة نصوص في مواضع محددة
- **تحويل إلى HTML**: إنشاء صفحات ويب جميلة
- **إضافة مقدمة/خاتمة**: تنسيق الأسطر
- **معالجة الأسطر الفارغة**: تنظيم المحتوى
- **تعديل الأسماء**: إعادة تسمية ذكية

### 3. تخصيص الإعدادات
- اضبط الخيارات حسب احتياجاتك
- استخدم الأمثلة الجاهزة للبدء السريع
- فعّل الذكاء الصناعي للنتائج المتقدمة

### 4. متابعة التقدم
- شاهد شريط التقدم المباشر
- أوقف أو استأنف العملية حسب الحاجة
- اطلع على تفاصيل كل خطوة

### 5. عرض النتائج
- استعرض النتائج في جداول منسقة
- ابحث في النتائج باستخدام محرك البحث
- صدّر النتائج بالصيغة المطلوبة

## الإعدادات المتقدمة

### مفاتيح الذكاء الصناعي
- تدوير تلقائي بين 15 مفتاح API
- إدارة ذكية للحدود والأخطاء
- تقسيم المحتوى الكبير تلقائياً

### خيارات التصدير
- تنسيق التاريخ (عربي/إنجليزي)
- تضمين الطابع الزمني
- تخصيص تنسيق الملفات

### إعدادات الأداء
- عدد الطلبات المتزامنة
- تأخير بين الطلبات
- إدارة الذاكرة المحسنة

## المتطلبات

- متصفح حديث يدعم ES6+
- اتصال بالإنترنت (للذكاء الصناعي)
- JavaScript مفعل

## المتصفحات المدعومة

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## الأمان والخصوصية

- جميع العمليات تتم محلياً في المتصفح
- لا يتم رفع الملفات إلى خوادم خارجية
- مفاتيح API محمية ومشفرة
- لا يتم حفظ البيانات الشخصية

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تحقق من وحدة التحكم في المتصفح للأخطاء
- تأكد من صحة اتصال الإنترنت
- جرب تحديث الصفحة أو تغيير المتصفح

## التحديثات المستقبلية

- دعم صيغ ملفات إضافية
- مزيد من قوالب HTML
- تحسينات في الذكاء الصناعي
- ميزات تعاونية متقدمة

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**تم تطوير هذه الأداة بعناية فائقة لتوفير تجربة استخدام مثالية للمستخدمين العرب**
