// ===== النماذج المتقدمة للمعالجة =====

class AdvancedForms {
    // نموذج استبدال النصوص
    static getReplaceForm() {
        return `
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">النص المراد استبداله</label>
                        <textarea class="form-control" id="searchText" rows="3" placeholder="أدخل النص المراد البحث عنه"></textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">النص البديل</label>
                        <textarea class="form-control" id="replaceText" rows="3" placeholder="أدخل النص البديل (اتركه فارغاً للحذف)"></textarea>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="caseSensitive">
                            <label class="form-check-label">حساس لحالة الأحرف</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="wholeWord">
                            <label class="form-check-label">كلمة كاملة فقط</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="useRegex">
                            <label class="form-check-label">استخدام التعبيرات النمطية</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">أمثلة شائعة للاستبدال</label>
                <div class="btn-group-vertical w-100" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="AdvancedForms.setReplaceExample('removeEmptyLines')">
                        إزالة الأسطر الفارغة
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="AdvancedForms.setReplaceExample('removeExtraSpaces')">
                        إزالة المسافات الزائدة
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="AdvancedForms.setReplaceExample('arabicToEnglishNumbers')">
                        تحويل الأرقام العربية إلى إنجليزية
                    </button>
                </div>
            </div>
            <button class="btn btn-primary btn-lg" onclick="mainApp.startReplacing()">
                <i class="bi bi-play-fill"></i> بدء الاستبدال
            </button>
        `;
    }

    // نموذج إضافة المحتوى
    static getAddContentForm() {
        return `
            <div class="form-group">
                <label class="form-label">المحتوى المراد إضافته</label>
                <textarea class="form-control" id="contentToAdd" rows="4" placeholder="أدخل المحتوى المراد إضافته"></textarea>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">موضع الإضافة</label>
                        <select class="form-select" id="addPosition">
                            <option value="beginning">بداية كل ملف</option>
                            <option value="end">نهاية كل ملف</option>
                            <option value="afterLine">بعد سطر محدد</option>
                            <option value="beforeLine">قبل سطر محدد</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">رقم السطر (إذا اخترت موضع محدد)</label>
                        <input type="number" class="form-control" id="lineNumber" min="1" placeholder="رقم السطر">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="addToAllFiles" checked>
                    <label class="form-check-label">تطبيق على جميع الملفات</label>
                </div>
            </div>
            <button class="btn btn-primary btn-lg" onclick="mainApp.startAddingContent()">
                <i class="bi bi-play-fill"></i> بدء الإضافة
            </button>
        `;
    }

    // نموذج تحويل إلى HTML
    static getToHtmlForm() {
        return `
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">قالب HTML</label>
                        <select class="form-select" id="htmlTemplate">
                            <option value="simple">بسيط</option>
                            <option value="modern" selected>حديث</option>
                            <option value="article">مقال</option>
                            <option value="blog">مدونة</option>
                            <option value="documentation">توثيق</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">نظام الألوان</label>
                        <select class="form-select" id="colorScheme">
                            <option value="blue">أزرق</option>
                            <option value="green">أخضر</option>
                            <option value="purple">بنفسجي</option>
                            <option value="dark">داكن</option>
                            <option value="light">فاتح</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeCSS" checked>
                            <label class="form-check-label">تضمين CSS مدمج</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="responsiveDesign" checked>
                            <label class="form-check-label">تصميم متجاوب</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="useAI" checked>
                    <label class="form-check-label">استخدام الذكاء الصناعي لتحسين التنسيق</label>
                </div>
            </div>
            <button class="btn btn-primary btn-lg" onclick="mainApp.startHtmlConversion()">
                <i class="bi bi-play-fill"></i> بدء التحويل
            </button>
        `;
    }

    // نموذج المقدمة والخاتمة
    static getAddPrefixForm() {
        return `
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">المقدمة (تضاف لبداية كل سطر)</label>
                        <textarea class="form-control" id="prefixText" rows="3" placeholder="مثال: • "></textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">الخاتمة (تضاف لنهاية كل سطر)</label>
                        <textarea class="form-control" id="suffixText" rows="3" placeholder="مثال: ."></textarea>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skipEmptyLines" checked>
                            <label class="form-check-label">تجاهل الأسطر الفارغة</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="addLineNumbers">
                            <label class="form-check-label">إضافة أرقام الأسطر</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">أمثلة شائعة</label>
                <div class="btn-group-vertical w-100" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="AdvancedForms.setPrefixExample('bullet')">
                        نقاط تعداد (•)
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="AdvancedForms.setPrefixExample('numbers')">
                        ترقيم (1. 2. 3.)
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="AdvancedForms.setPrefixExample('quotes')">
                        علامات اقتباس ("")
                    </button>
                </div>
            </div>
            <button class="btn btn-primary btn-lg" onclick="mainApp.startAddingPrefix()">
                <i class="bi bi-play-fill"></i> بدء الإضافة
            </button>
        `;
    }

    // نموذج الأسطر الفارغة
    static getEmptyLinesForm() {
        return `
            <div class="form-group">
                <label class="form-label">العملية المطلوبة</label>
                <select class="form-select" id="emptyLineOperation">
                    <option value="remove">إزالة جميع الأسطر الفارغة</option>
                    <option value="removeExtra">إزالة الأسطر الفارغة الزائدة (ترك سطر واحد فقط)</option>
                    <option value="add">إضافة سطر فارغ بين كل سطرين</option>
                    <option value="addAfterParagraph">إضافة سطر فارغ بعد كل فقرة</option>
                    <option value="normalize">توحيد الأسطر الفارغة</option>
                </select>
            </div>
            <div class="form-group" id="normalizeOptions" style="display: none;">
                <label class="form-label">عدد الأسطر الفارغة المطلوبة</label>
                <input type="number" class="form-control" id="emptyLineCount" min="0" max="5" value="1">
            </div>
            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="preserveStructure" checked>
                    <label class="form-check-label">الحفاظ على بنية النص</label>
                </div>
            </div>
            <button class="btn btn-primary btn-lg" onclick="mainApp.startEmptyLinesProcessing()">
                <i class="bi bi-play-fill"></i> بدء المعالجة
            </button>
        `;
    }

    // نموذج تعديل الأسماء
    static getRenameForm() {
        return `
            <div class="form-group">
                <label class="form-label">طريقة التسمية</label>
                <select class="form-select" id="renamingMethod">
                    <option value="manual">يدوي</option>
                    <option value="ai" selected>بالذكاء الصناعي</option>
                    <option value="pattern">نمط محدد</option>
                    <option value="content">بناءً على المحتوى</option>
                </select>
            </div>
            
            <div id="manualRenaming" style="display: none;">
                <div class="form-group">
                    <label class="form-label">قائمة الأسماء الجديدة (سطر لكل اسم)</label>
                    <textarea class="form-control" id="newNames" rows="5" placeholder="اسم الملف الأول
اسم الملف الثاني
..."></textarea>
                </div>
            </div>
            
            <div id="aiRenaming">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">طول الاسم المطلوب</label>
                            <select class="form-select" id="nameLength">
                                <option value="short">قصير (20-30 حرف)</option>
                                <option value="medium" selected>متوسط (30-50 حرف)</option>
                                <option value="long">طويل (50-80 حرف)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">نمط التسمية</label>
                            <select class="form-select" id="namingStyle">
                                <option value="descriptive" selected>وصفي</option>
                                <option value="formal">رسمي</option>
                                <option value="simple">بسيط</option>
                                <option value="creative">إبداعي</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="patternRenaming" style="display: none;">
                <div class="form-group">
                    <label class="form-label">نمط التسمية</label>
                    <input type="text" class="form-control" id="namingPattern" placeholder="مثال: ملف_{رقم}_{تاريخ}">
                    <small class="form-text text-muted">
                        استخدم: {رقم} للترقيم، {تاريخ} للتاريخ، {وقت} للوقت
                    </small>
                </div>
            </div>
            
            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="removeSpecialChars" checked>
                    <label class="form-check-label">إزالة الأحرف الخاصة</label>
                </div>
            </div>
            
            <button class="btn btn-primary btn-lg" onclick="mainApp.startRenaming()">
                <i class="bi bi-play-fill"></i> بدء إعادة التسمية
            </button>
        `;
    }

    // تعيين مثال للاستبدال
    static setReplaceExample(type) {
        const searchText = document.getElementById('searchText');
        const replaceText = document.getElementById('replaceText');
        const useRegex = document.getElementById('useRegex');

        switch (type) {
            case 'removeEmptyLines':
                searchText.value = '^\\s*$\\n';
                replaceText.value = '';
                useRegex.checked = true;
                break;
            case 'removeExtraSpaces':
                searchText.value = '\\s+';
                replaceText.value = ' ';
                useRegex.checked = true;
                break;
            case 'arabicToEnglishNumbers':
                searchText.value = '[٠-٩]';
                replaceText.value = '';
                useRegex.checked = true;
                Utils.showAlert('سيتم تحويل الأرقام العربية إلى إنجليزية', 'info');
                break;
        }
    }

    // تعيين مثال للمقدمة
    static setPrefixExample(type) {
        const prefixText = document.getElementById('prefixText');
        const suffixText = document.getElementById('suffixText');

        switch (type) {
            case 'bullet':
                prefixText.value = '• ';
                suffixText.value = '';
                break;
            case 'numbers':
                prefixText.value = '{رقم}. ';
                suffixText.value = '';
                break;
            case 'quotes':
                prefixText.value = '"';
                suffixText.value = '"';
                break;
        }
    }

    // إعداد مستمعي الأحداث للنماذج
    static setupFormListeners() {
        // مستمع لتغيير طريقة إعادة التسمية
        const renamingMethod = document.getElementById('renamingMethod');
        if (renamingMethod) {
            renamingMethod.addEventListener('change', (e) => {
                this.toggleRenamingMethod(e.target.value);
            });
        }

        // مستمع لتغيير عملية الأسطر الفارغة
        const emptyLineOperation = document.getElementById('emptyLineOperation');
        if (emptyLineOperation) {
            emptyLineOperation.addEventListener('change', (e) => {
                this.toggleEmptyLineOptions(e.target.value);
            });
        }
    }

    // تبديل طريقة إعادة التسمية
    static toggleRenamingMethod(method) {
        const manualDiv = document.getElementById('manualRenaming');
        const aiDiv = document.getElementById('aiRenaming');
        const patternDiv = document.getElementById('patternRenaming');

        // إخفاء جميع الأقسام
        if (manualDiv) manualDiv.style.display = 'none';
        if (aiDiv) aiDiv.style.display = 'none';
        if (patternDiv) patternDiv.style.display = 'none';

        // إظهار القسم المناسب
        switch (method) {
            case 'manual':
                if (manualDiv) manualDiv.style.display = 'block';
                break;
            case 'ai':
            case 'content':
                if (aiDiv) aiDiv.style.display = 'block';
                break;
            case 'pattern':
                if (patternDiv) patternDiv.style.display = 'block';
                break;
        }
    }

    // تبديل خيارات الأسطر الفارغة
    static toggleEmptyLineOptions(operation) {
        const normalizeOptions = document.getElementById('normalizeOptions');
        if (normalizeOptions) {
            normalizeOptions.style.display = operation === 'normalize' ? 'block' : 'none';
        }
    }
}

// إعداد مستمعي الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    AdvancedForms.setupFormListeners();
});
