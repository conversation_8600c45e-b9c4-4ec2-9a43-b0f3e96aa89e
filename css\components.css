/* ===== مكونات مخصصة ===== */

/* ===== Tables ===== */
.results-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.results-table th {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    border: none;
}

.results-table td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    text-align: right;
}

.results-table tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

.results-table tr:last-child td {
    border-bottom: none;
}

/* ===== Form Components ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
}

.form-select {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center;
    background-size: 16px 12px;
    padding-left: 2.5rem;
}

.form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.form-check-input {
    margin-left: 0.5rem;
    margin-right: 0;
}

.form-check-label {
    margin-bottom: 0;
    cursor: pointer;
}

/* ===== Buttons ===== */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.btn i {
    margin-left: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--accent-color);
    color: white;
}

.btn-outline-light {
    border: 2px solid white;
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: white;
    color: var(--dark-color);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

/* ===== Alerts ===== */
.alert {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    border: none;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.alert i {
    margin-left: 0.5rem;
    font-size: 1.2rem;
}

.alert-success {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border-right: 4px solid var(--success-color);
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border-right: 4px solid var(--warning-color);
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    color: var(--accent-color);
    border-right: 4px solid var(--accent-color);
}

.alert-info {
    background: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
    border-right: 4px solid var(--secondary-color);
}

/* ===== Badges ===== */
.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: var(--secondary-color);
    color: white;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--accent-color);
    color: white;
}

/* ===== Tooltips ===== */
.tooltip-container {
    position: relative;
    display: inline-block;
}

.tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: var(--dark-color);
    color: white;
    text-align: center;
    border-radius: var(--border-radius);
    padding: 0.5rem;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    right: 50%;
    margin-right: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.875rem;
}

.tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    right: 50%;
    margin-right: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--dark-color) transparent transparent transparent;
}

.tooltip-container:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* ===== Loading Spinner ===== */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spinner-sm {
    width: 20px;
    height: 20px;
    border-width: 2px;
}

.spinner-lg {
    width: 60px;
    height: 60px;
    border-width: 6px;
}

/* ===== Modal Enhancements ===== */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--shadow-heavy);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-bottom: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-title {
    font-weight: 600;
}

.btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1.5rem 2rem;
}

/* ===== File Preview ===== */
.file-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1rem 0;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
}

.file-preview .line-number {
    color: #6c757d;
    margin-left: 1rem;
    user-select: none;
}

.file-preview .line-content {
    white-space: pre-wrap;
}

/* ===== Statistics Cards ===== */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border-right: 4px solid var(--secondary-color);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

/* ===== Code Block ===== */
.code-block {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1rem 0;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    position: relative;
}

.code-block::before {
    content: attr(data-language);
    position: absolute;
    top: 0.5rem;
    left: 1rem;
    background: var(--secondary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.code-block pre {
    margin: 0;
    padding-top: 2rem;
}

/* ===== Accordion ===== */
.accordion-item {
    border: none;
    margin-bottom: 0.5rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.accordion-header {
    margin-bottom: 0;
}

.accordion-button {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 600;
    text-align: right;
}

.accordion-button:not(.collapsed) {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: none;
}

.accordion-button::after {
    margin-right: auto;
    margin-left: 0;
}

.accordion-body {
    padding: 1.5rem;
    background: white;
}

/* ===== Tabs ===== */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 1.5rem;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 1rem 1.5rem;
    color: #6c757d;
    font-weight: 600;
    transition: var(--transition);
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: var(--secondary-color);
    background: rgba(52, 152, 219, 0.1);
}

.nav-tabs .nav-link.active {
    color: var(--secondary-color);
    background: white;
    border-color: var(--secondary-color) var(--secondary-color) white;
    border-width: 2px 2px 2px 2px;
}

/* ===== Responsive Utilities ===== */
@media (max-width: 576px) {
    .results-table {
        font-size: 0.875rem;
    }
    
    .results-table th,
    .results-table td {
        padding: 0.75rem 0.5rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}
