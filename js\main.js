// ===== الملف الرئيسي للتطبيق =====

class MainApp {
    constructor() {
        this.currentOperation = null;
        this.isProcessing = false;
        this.init();
    }

    // تهيئة التطبيق
    init() {
        this.setupEventListeners();
        this.setupDragAndDrop();
        this.setupSettings();
        this.loadSettings();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // رفع الملفات
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelect(e.target.files);
            });
        }

        // خيارات المعالجة
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('click', () => {
                this.selectOption(card.dataset.option);
            });
        });

        // أزرار التحكم في التقدم
        const pauseBtn = document.getElementById('pauseBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        
        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => {
                this.togglePause();
            });
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.cancelProcessing();
            });
        }

        // أزرار التصدير
        this.setupExportButtons();

        // الإعدادات
        const settingsBtn = document.getElementById('settingsBtn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.openSettings();
            });
        }

        // البحث في النتائج
        const searchResults = document.getElementById('searchResults');
        if (searchResults) {
            searchResults.addEventListener('input', (e) => {
                this.searchInResults(e.target.value);
            });
        }
    }

    // إعداد السحب والإفلات
    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files).filter(file => 
                file.type === 'text/plain' || file.name.endsWith('.txt')
            );
            
            if (files.length > 0) {
                this.handleFileSelect(files);
            } else {
                Utils.showAlert('يرجى رفع ملفات نصية فقط (.txt)', 'warning');
            }
        });

        // النقر على منطقة الرفع
        uploadArea.addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
    }

    // معالجة اختيار الملفات
    handleFileSelect(files) {
        if (files.length === 0) return;

        const txtFiles = Array.from(files).filter(file => 
            file.type === 'text/plain' || file.name.endsWith('.txt')
        );

        if (txtFiles.length === 0) {
            Utils.showAlert('لم يتم العثور على ملفات نصية صالحة', 'warning');
            return;
        }

        if (txtFiles.length !== files.length) {
            Utils.showAlert(`تم تجاهل ${files.length - txtFiles.length} ملف غير نصي`, 'info');
        }

        fileProcessor.addFiles(txtFiles);
        Utils.showAlert(`تم رفع ${txtFiles.length} ملف بنجاح`, 'success');
    }

    // اختيار خيار المعالجة
    selectOption(option) {
        // إزالة التحديد السابق
        document.querySelectorAll('.option-card').forEach(card => {
            card.classList.remove('selected');
        });

        // تحديد الخيار الحالي
        const selectedCard = document.querySelector(`[data-option="${option}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        this.currentOperation = option;
        this.showProcessingForm(option);
    }

    // عرض نموذج المعالجة
    showProcessingForm(option) {
        const formSection = document.getElementById('processingForm');
        const formTitle = document.getElementById('formTitle');
        const formContent = document.getElementById('formContent');

        if (!formSection || !formTitle || !formContent) return;

        formTitle.innerHTML = this.getFormTitle(option);
        formContent.innerHTML = this.getFormContent(option);

        formSection.style.display = 'block';
        formSection.scrollIntoView({ behavior: 'smooth' });
    }

    // الحصول على عنوان النموذج
    getFormTitle(option) {
        const titles = {
            'analyze': '<i class="bi bi-graph-up"></i> إعدادات التحليل التفصيلي',
            'count': '<i class="bi bi-list-ol"></i> إعدادات عد الأسطر',
            'merge': '<i class="bi bi-union"></i> إعدادات دمج الملفات',
            'replace': '<i class="bi bi-arrow-repeat"></i> إعدادات استبدال النصوص',
            'addContent': '<i class="bi bi-plus-circle"></i> إعدادات إضافة المحتوى',
            'toHtml': '<i class="bi bi-code-slash"></i> إعدادات تحويل إلى HTML',
            'addPrefix': '<i class="bi bi-text-left"></i> إعدادات المقدمة والخاتمة',
            'emptyLines': '<i class="bi bi-text-paragraph"></i> إعدادات الأسطر الفارغة',
            'rename': '<i class="bi bi-pencil-square"></i> إعدادات تعديل الأسماء'
        };
        return titles[option] || '<i class="bi bi-gear"></i> إعدادات المعالجة';
    }

    // الحصول على محتوى النموذج
    getFormContent(option) {
        switch (option) {
            case 'analyze':
                return this.getAnalyzeForm();
            case 'count':
                return this.getCountForm();
            case 'merge':
                return this.getMergeForm();
            case 'replace':
                return AdvancedForms.getReplaceForm();
            case 'addContent':
                return AdvancedForms.getAddContentForm();
            case 'toHtml':
                return AdvancedForms.getToHtmlForm();
            case 'addPrefix':
                return AdvancedForms.getAddPrefixForm();
            case 'emptyLines':
                return AdvancedForms.getEmptyLinesForm();
            case 'rename':
                return AdvancedForms.getRenameForm();
            default:
                return '<p>خيار غير مدعوم</p>';
        }
    }

    // نموذج التحليل
    getAnalyzeForm() {
        return `
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">نوع التحليل</label>
                        <select class="form-select" id="analysisType">
                            <option value="comprehensive">تحليل شامل</option>
                            <option value="basic">تحليل أساسي</option>
                            <option value="advanced">تحليل متقدم</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">تضمين الكلمات المفتاحية</label>
                        <select class="form-select" id="keywordCount">
                            <option value="5">5 كلمات</option>
                            <option value="10" selected>10 كلمات</option>
                            <option value="15">15 كلمة</option>
                            <option value="20">20 كلمة</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="includeStats" checked>
                    <label class="form-check-label">تضمين الإحصائيات التفصيلية</label>
                </div>
            </div>
            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="generateReport" checked>
                    <label class="form-check-label">إنشاء تقرير شامل</label>
                </div>
            </div>
            <button class="btn btn-primary btn-lg" onclick="mainApp.startAnalysis()">
                <i class="bi bi-play-fill"></i> بدء التحليل
            </button>
        `;
    }

    // نموذج عد الأسطر
    getCountForm() {
        return `
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="countWords" checked>
                            <label class="form-check-label">عد الكلمات</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="countChars" checked>
                            <label class="form-check-label">عد الأحرف</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="countEmptyLines" checked>
                            <label class="form-check-label">عد الأسطر الفارغة</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showStatistics" checked>
                            <label class="form-check-label">عرض الإحصائيات</label>
                        </div>
                    </div>
                </div>
            </div>
            <button class="btn btn-primary btn-lg" onclick="mainApp.startCounting()">
                <i class="bi bi-play-fill"></i> بدء العد
            </button>
        `;
    }

    // نموذج الدمج
    getMergeForm() {
        return `
            <div class="form-group">
                <label class="form-label">فاصل بين الملفات</label>
                <textarea class="form-control" id="fileSeparator" rows="3" placeholder="أدخل النص الفاصل بين الملفات">

--- الملف التالي ---

</textarea>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeFilenames" checked>
                            <label class="form-check-label">تضمين أسماء الملفات</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="addTimestamp">
                            <label class="form-check-label">إضافة الطابع الزمني</label>
                        </div>
                    </div>
                </div>
            </div>
            <button class="btn btn-primary btn-lg" onclick="mainApp.startMerging()">
                <i class="bi bi-play-fill"></i> بدء الدمج
            </button>
        `;
    }

    // بدء التحليل
    async startAnalysis() {
        if (fileProcessor.files.length === 0) {
            Utils.showAlert('يرجى رفع ملفات أولاً', 'warning');
            return;
        }

        this.isProcessing = true;
        try {
            await fileProcessor.analyzeFiles();
            exportUtils.setCurrentResults(fileProcessor.files, 'analysis');
        } catch (error) {
            Utils.showAlert('فشل في التحليل: ' + error.message, 'danger');
        } finally {
            this.isProcessing = false;
        }
    }

    // بدء العد
    async startCounting() {
        if (fileProcessor.files.length === 0) {
            Utils.showAlert('يرجى رفع ملفات أولاً', 'warning');
            return;
        }

        this.isProcessing = true;
        try {
            await fileProcessor.countLines();
            exportUtils.setCurrentResults(fileProcessor.files, 'lineCount');
        } catch (error) {
            Utils.showAlert('فشل في العد: ' + error.message, 'danger');
        } finally {
            this.isProcessing = false;
        }
    }

    // بدء الدمج
    async startMerging() {
        if (fileProcessor.files.length === 0) {
            Utils.showAlert('يرجى رفع ملفات أولاً', 'warning');
            return;
        }

        const options = {
            separator: document.getElementById('fileSeparator')?.value || '\n\n--- الملف التالي ---\n\n',
            includeFilenames: document.getElementById('includeFilenames')?.checked || false,
            addTimestamp: document.getElementById('addTimestamp')?.checked || false
        };

        this.isProcessing = true;
        try {
            await fileProcessor.mergeFiles(options);
        } catch (error) {
            Utils.showAlert('فشل في الدمج: ' + error.message, 'danger');
        } finally {
            this.isProcessing = false;
        }
    }

    // بدء الاستبدال
    async startReplacing() {
        if (fileProcessor.files.length === 0) {
            Utils.showAlert('يرجى رفع ملفات أولاً', 'warning');
            return;
        }

        const searchText = document.getElementById('searchText')?.value;
        if (!searchText) {
            Utils.showAlert('يرجى إدخال النص المراد البحث عنه', 'warning');
            return;
        }

        const options = {
            searchText: searchText,
            replaceWith: document.getElementById('replaceText')?.value || '',
            caseSensitive: document.getElementById('caseSensitive')?.checked || false,
            wholeWord: document.getElementById('wholeWord')?.checked || false,
            useRegex: document.getElementById('useRegex')?.checked || false
        };

        this.isProcessing = true;
        try {
            await fileProcessor.replaceText(options);
            exportUtils.setCurrentResults(fileProcessor.processedResults, 'replace');
        } catch (error) {
            Utils.showAlert('فشل في الاستبدال: ' + error.message, 'danger');
        } finally {
            this.isProcessing = false;
        }
    }

    // بدء إضافة المحتوى
    async startAddingContent() {
        if (fileProcessor.files.length === 0) {
            Utils.showAlert('يرجى رفع ملفات أولاً', 'warning');
            return;
        }

        const contentToAdd = document.getElementById('contentToAdd')?.value;
        if (!contentToAdd) {
            Utils.showAlert('يرجى إدخال المحتوى المراد إضافته', 'warning');
            return;
        }

        const options = {
            content: contentToAdd,
            position: document.getElementById('addPosition')?.value || 'beginning',
            lineNumber: parseInt(document.getElementById('lineNumber')?.value) || 1,
            applyToAll: document.getElementById('addToAllFiles')?.checked || true
        };

        this.isProcessing = true;
        try {
            await fileProcessor.addContent(options);
        } catch (error) {
            Utils.showAlert('فشل في إضافة المحتوى: ' + error.message, 'danger');
        } finally {
            this.isProcessing = false;
        }
    }

    // بدء تحويل HTML
    async startHtmlConversion() {
        if (fileProcessor.files.length === 0) {
            Utils.showAlert('يرجى رفع ملفات أولاً', 'warning');
            return;
        }

        const options = {
            template: document.getElementById('htmlTemplate')?.value || 'modern',
            colorScheme: document.getElementById('colorScheme')?.value || 'blue',
            includeCSS: document.getElementById('includeCSS')?.checked || true,
            responsive: document.getElementById('responsiveDesign')?.checked || true,
            useAI: document.getElementById('useAI')?.checked || true
        };

        this.isProcessing = true;
        try {
            await fileProcessor.convertToHtml(options);
        } catch (error) {
            Utils.showAlert('فشل في تحويل HTML: ' + error.message, 'danger');
        } finally {
            this.isProcessing = false;
        }
    }

    // بدء إضافة المقدمة والخاتمة
    async startAddingPrefix() {
        if (fileProcessor.files.length === 0) {
            Utils.showAlert('يرجى رفع ملفات أولاً', 'warning');
            return;
        }

        const prefixText = document.getElementById('prefixText')?.value || '';
        const suffixText = document.getElementById('suffixText')?.value || '';

        if (!prefixText && !suffixText) {
            Utils.showAlert('يرجى إدخال مقدمة أو خاتمة على الأقل', 'warning');
            return;
        }

        const options = {
            prefix: prefixText,
            suffix: suffixText,
            skipEmptyLines: document.getElementById('skipEmptyLines')?.checked || true,
            addLineNumbers: document.getElementById('addLineNumbers')?.checked || false
        };

        this.isProcessing = true;
        try {
            await fileProcessor.addPrefixSuffix(options);
        } catch (error) {
            Utils.showAlert('فشل في إضافة المقدمة/الخاتمة: ' + error.message, 'danger');
        } finally {
            this.isProcessing = false;
        }
    }

    // بدء معالجة الأسطر الفارغة
    async startEmptyLinesProcessing() {
        if (fileProcessor.files.length === 0) {
            Utils.showAlert('يرجى رفع ملفات أولاً', 'warning');
            return;
        }

        const options = {
            operation: document.getElementById('emptyLineOperation')?.value || 'remove',
            emptyLineCount: parseInt(document.getElementById('emptyLineCount')?.value) || 1,
            preserveStructure: document.getElementById('preserveStructure')?.checked || true
        };

        this.isProcessing = true;
        try {
            await fileProcessor.processEmptyLines(options);
        } catch (error) {
            Utils.showAlert('فشل في معالجة الأسطر الفارغة: ' + error.message, 'danger');
        } finally {
            this.isProcessing = false;
        }
    }

    // بدء إعادة التسمية
    async startRenaming() {
        if (fileProcessor.files.length === 0) {
            Utils.showAlert('يرجى رفع ملفات أولاً', 'warning');
            return;
        }

        const method = document.getElementById('renamingMethod')?.value || 'ai';
        const options = {
            method: method,
            nameLength: document.getElementById('nameLength')?.value || 'medium',
            namingStyle: document.getElementById('namingStyle')?.value || 'descriptive',
            namingPattern: document.getElementById('namingPattern')?.value || '',
            newNames: document.getElementById('newNames')?.value || '',
            removeSpecialChars: document.getElementById('removeSpecialChars')?.checked || true
        };

        this.isProcessing = true;
        try {
            await fileProcessor.renameFiles(options);
        } catch (error) {
            Utils.showAlert('فشل في إعادة التسمية: ' + error.message, 'danger');
        } finally {
            this.isProcessing = false;
        }
    }

    // إيقاف/استئناف المعالجة
    togglePause() {
        const pauseBtn = document.getElementById('pauseBtn');
        if (!pauseBtn) return;

        if (fileProcessor.processingPaused) {
            fileProcessor.resumeProcessing();
            pauseBtn.innerHTML = '<i class="bi bi-pause"></i> إيقاف مؤقت';
        } else {
            fileProcessor.pauseProcessing();
            pauseBtn.innerHTML = '<i class="bi bi-play"></i> استئناف';
        }
    }

    // إلغاء المعالجة
    cancelProcessing() {
        fileProcessor.cancelProcessing();
        this.isProcessing = false;
    }

    // البحث في النتائج
    searchInResults(query) {
        const resultsTable = document.querySelector('.results-table tbody');
        if (!resultsTable) return;

        const rows = resultsTable.querySelectorAll('tr');
        const searchTerm = query.toLowerCase();

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    }

    // إعداد أزرار التصدير
    setupExportButtons() {
        const exportHtml = document.getElementById('exportHtml');
        const exportExcel = document.getElementById('exportExcel');
        const exportPdf = document.getElementById('exportPdf');
        const exportZip = document.getElementById('exportZip');

        if (exportHtml) {
            exportHtml.addEventListener('click', () => exportUtils.exportToHtml());
        }
        
        if (exportExcel) {
            exportExcel.addEventListener('click', () => exportUtils.exportToExcel());
        }
        
        if (exportPdf) {
            exportPdf.addEventListener('click', () => exportUtils.exportToPdf());
        }
        
        if (exportZip) {
            exportZip.addEventListener('click', () => exportUtils.exportToZip());
        }
    }

    // إعداد الإعدادات
    setupSettings() {
        const exportSettings = document.getElementById('exportSettings');
        const importSettings = document.getElementById('importSettings');
        const settingsFile = document.getElementById('settingsFile');

        if (exportSettings) {
            exportSettings.addEventListener('click', () => exportUtils.exportSettings());
        }

        if (importSettings) {
            importSettings.addEventListener('click', () => settingsFile.click());
        }

        if (settingsFile) {
            settingsFile.addEventListener('change', (e) => {
                if (e.target.files[0]) {
                    exportUtils.importSettings(e.target.files[0]);
                }
            });
        }
    }

    // فتح الإعدادات
    openSettings() {
        const settingsModal = new bootstrap.Modal(document.getElementById('settingsModal'));
        settingsModal.show();
    }

    // تحميل الإعدادات
    loadSettings() {
        // تحميل مفاتيح API
        const currentApiKey = document.getElementById('currentApiKey');
        if (currentApiKey && fileProcessor.aiIntegration) {
            currentApiKey.innerHTML = fileProcessor.aiIntegration.apiKeys.map((key, index) => 
                `<option value="${index}" ${index === fileProcessor.aiIntegration.currentKeyIndex ? 'selected' : ''}>
                    مفتاح API ${index + 1}
                </option>`
            ).join('');
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.mainApp = new MainApp();
});

// إضافة مستمع للأخطاء العامة
window.addEventListener('error', (e) => {
    console.error('خطأ في التطبيق:', e.error);
    Utils.showAlert('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', 'danger');
});

// إضافة مستمع لأخطاء الشبكة
window.addEventListener('unhandledrejection', (e) => {
    console.error('خطأ في الشبكة:', e.reason);
    Utils.showAlert('فشل في الاتصال. تحقق من اتصال الإنترنت.', 'warning');
});
