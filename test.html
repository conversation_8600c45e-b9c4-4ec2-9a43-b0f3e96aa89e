<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للأداة</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 2rem;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 10px;
            border-right: 4px solid #ffd700;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 0.5rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .status {
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 5px;
        }
        .success { background: rgba(39, 174, 96, 0.3); }
        .error { background: rgba(231, 76, 60, 0.3); }
        .info { background: rgba(52, 152, 219, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار سريع لأداة معالجة النصوص</h1>
        
        <div class="test-section">
            <h3>📁 الملفات التجريبية</h3>
            <p>تم إنشاء 3 ملفات تجريبية في مجلد test-files:</p>
            <ul>
                <li><strong>sample1.txt</strong> - مقال عن الذكاء الصناعي</li>
                <li><strong>sample2.txt</strong> - مقال عن تطوير المواقع</li>
                <li><strong>sample3.txt</strong> - مقال عن التعليم الرقمي</li>
            </ul>
            <div class="status info">
                ✅ يمكنك استخدام هذه الملفات لاختبار جميع وظائف الأداة
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 الوظائف المصلحة</h3>
            <div class="status success">
                ✅ تم إصلاح مشكلة عرض "undefined" و "ليس رقم" في النتائج
            </div>
            <div class="status success">
                ✅ تم إصلاح مشكلة تصدير الملفات المعاد تسميتها
            </div>
            <div class="status success">
                ✅ تم تحسين معالجة الأخطاء وقراءة الملفات
            </div>
            <div class="status success">
                ✅ تم إضافة زر تحميل جميع الملفات المعاد تسميتها
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 خطوات الاختبار المقترحة</h3>
            <ol>
                <li><strong>اختبار رفع الملفات:</strong> ارفع الملفات التجريبية</li>
                <li><strong>اختبار عد الأسطر:</strong> تأكد من ظهور الأرقام الصحيحة</li>
                <li><strong>اختبار التحليل:</strong> جرب التحليل بالذكاء الصناعي</li>
                <li><strong>اختبار إعادة التسمية:</strong> جرب إعادة التسمية وتحميل الملفات</li>
                <li><strong>اختبار التصدير:</strong> جرب تصدير النتائج بصيغ مختلفة</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>⚠️ ملاحظات مهمة</h3>
            <div class="status info">
                🔑 تأكد من وجود اتصال إنترنت لاستخدام الذكاء الصناعي
            </div>
            <div class="status info">
                📱 الأداة تعمل على جميع المتصفحات الحديثة
            </div>
            <div class="status info">
                🔒 جميع العمليات تتم محلياً في المتصفح (آمنة)
            </div>
        </div>

        <div style="text-align: center; margin-top: 2rem;">
            <a href="index.html" class="btn">🚀 تشغيل الأداة الآن</a>
            <a href="start.html" class="btn">📖 صفحة البداية</a>
        </div>

        <div style="text-align: center; margin-top: 2rem; opacity: 0.8; font-size: 0.9rem;">
            <p>تم إصلاح جميع المشاكل المبلغ عنها ✨</p>
            <p>الأداة جاهزة للاستخدام الاحترافي 🎉</p>
        </div>
    </div>

    <script>
        // اختبار سريع للتأكد من تحميل المكتبات
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 بدء اختبار الأداة...');
            
            // اختبار وجود المكتبات الخارجية
            const libraries = [
                { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined' },
                { name: 'jsPDF', check: () => typeof window.jspdf !== 'undefined' },
                { name: 'JSZip', check: () => typeof JSZip !== 'undefined' }
            ];
            
            libraries.forEach(lib => {
                try {
                    if (lib.check()) {
                        console.log(`✅ ${lib.name} محمل بنجاح`);
                    } else {
                        console.log(`⚠️ ${lib.name} غير محمل`);
                    }
                } catch (e) {
                    console.log(`❌ خطأ في فحص ${lib.name}:`, e.message);
                }
            });
            
            console.log('🎉 انتهى الاختبار السريع');
        });
    </script>
</body>
</html>
