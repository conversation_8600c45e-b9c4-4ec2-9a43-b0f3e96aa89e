<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة معالجة ملفات النصوص المتقدمة - تحليل ومعالجة احترافية</title>
    <meta name="description" content="أداة احترافية لمعالجة وتحليل ملفات النصوص بالذكاء الصناعي. دمج، تحليل، تحويل وتصدير ملفات TXT بطرق متقدمة ومميزة.">
    <meta name="keywords" content="معالجة ملفات النصوص, تحليل النصوص, دمج الملفات, تحويل TXT, ذكاء صناعي">
    <meta name="author" content="أداة معالجة النصوص المتقدمة">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="أداة معالجة ملفات النصوص المتقدمة">
    <meta property="og:description" content="أداة احترافية لمعالجة وتحليل ملفات النصوص بالذكاء الصناعي">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="ar_AR">
    
    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "أداة معالجة ملفات النصوص المتقدمة",
        "description": "أداة احترافية لمعالجة وتحليل ملفات النصوص بالذكاء الصناعي",
        "applicationCategory": "ProductivityApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "author": {
            "@type": "Organization",
            "name": "أداة معالجة النصوص"
        }
    }
    </script>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/styles.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="header-title">
                        <i class="bi bi-file-text-fill"></i>
                        أداة معالجة ملفات النصوص المتقدمة
                    </h1>
                    <p class="header-subtitle">معالجة وتحليل ملفات النصوص بالذكاء الصناعي</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light" id="settingsBtn">
                        <i class="bi bi-gear"></i> الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- File Upload Section -->
            <section class="upload-section">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="bi bi-cloud-upload"></i> رفع الملفات</h3>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content">
                                <i class="bi bi-cloud-upload upload-icon"></i>
                                <h4>اسحب وأفلت ملفات TXT هنا</h4>
                                <p>أو انقر لاختيار الملفات</p>
                                <input type="file" id="fileInput" multiple accept=".txt" hidden>
                                <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                    <i class="bi bi-folder-open"></i> اختيار الملفات
                                </button>
                            </div>
                        </div>
                        <div class="uploaded-files" id="uploadedFiles"></div>
                    </div>
                </div>
            </section>

            <!-- Processing Options -->
            <section class="options-section" id="optionsSection" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="bi bi-tools"></i> خيارات المعالجة</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="option-card" data-option="analyze">
                                    <i class="bi bi-graph-up"></i>
                                    <h5>تحليل تفصيلي</h5>
                                    <p>تحليل جميع الملفات وإعطاء نظرة تفصيلية</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="option-card" data-option="count">
                                    <i class="bi bi-list-ol"></i>
                                    <h5>عد الأسطر</h5>
                                    <p>حساب عدد الأسطر في كل ملف</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="option-card" data-option="merge">
                                    <i class="bi bi-union"></i>
                                    <h5>دمج الملفات</h5>
                                    <p>دمج جميع الملفات في ملف واحد</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="option-card" data-option="replace">
                                    <i class="bi bi-arrow-repeat"></i>
                                    <h5>استبدال النصوص</h5>
                                    <p>استبدال أو حذف عبارات وجمل</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="option-card" data-option="addContent">
                                    <i class="bi bi-plus-circle"></i>
                                    <h5>إضافة محتوى</h5>
                                    <p>إضافة سطور في بداية أو نهاية الملفات</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="option-card" data-option="toHtml">
                                    <i class="bi bi-code-slash"></i>
                                    <h5>تحويل إلى HTML</h5>
                                    <p>تحويل الملفات إلى صفحات HTML مميزة</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="option-card" data-option="addPrefix">
                                    <i class="bi bi-text-left"></i>
                                    <h5>إضافة مقدمة/خاتمة</h5>
                                    <p>إضافة مقدمة أو خاتمة لكل الأسطر</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="option-card" data-option="emptyLines">
                                    <i class="bi bi-text-paragraph"></i>
                                    <h5>معالجة الأسطر الفارغة</h5>
                                    <p>إضافة أو حذف الأسطر الفارغة</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="option-card" data-option="rename">
                                    <i class="bi bi-pencil-square"></i>
                                    <h5>تعديل أسماء الملفات</h5>
                                    <p>تعديل أسماء الملفات يدوياً أو بالذكاء الصناعي</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Processing Form -->
            <section class="processing-form" id="processingForm" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h3 id="formTitle"><i class="bi bi-gear"></i> إعدادات المعالجة</h3>
                    </div>
                    <div class="card-body" id="formContent">
                        <!-- Dynamic form content will be inserted here -->
                    </div>
                </div>
            </section>

            <!-- Progress Section -->
            <section class="progress-section" id="progressSection" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="bi bi-hourglass-split"></i> تقدم العملية</h3>
                    </div>
                    <div class="card-body">
                        <div class="progress-container">
                            <div class="progress mb-3">
                                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="progress-info">
                                <span id="progressText">جاري التحضير...</span>
                                <span id="progressPercent">0%</span>
                            </div>
                            <div class="progress-controls mt-3">
                                <button class="btn btn-warning" id="pauseBtn">
                                    <i class="bi bi-pause"></i> إيقاف مؤقت
                                </button>
                                <button class="btn btn-danger" id="cancelBtn">
                                    <i class="bi bi-stop"></i> إلغاء
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3><i class="bi bi-check-circle"></i> النتائج</h3>
                        <div class="export-buttons">
                            <button class="btn btn-success btn-sm" id="exportHtml">
                                <i class="bi bi-file-earmark-code"></i> HTML
                            </button>
                            <button class="btn btn-success btn-sm" id="exportExcel">
                                <i class="bi bi-file-earmark-excel"></i> Excel
                            </button>
                            <button class="btn btn-success btn-sm" id="exportPdf">
                                <i class="bi bi-file-earmark-pdf"></i> PDF
                            </button>
                            <button class="btn btn-success btn-sm" id="exportZip">
                                <i class="bi bi-file-earmark-zip"></i> ZIP
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="search-container mb-3">
                            <input type="text" class="form-control" id="searchResults" placeholder="البحث في النتائج...">
                        </div>
                        <div id="resultsContent">
                            <!-- Results will be displayed here -->
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">الإعدادات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إعدادات الذكاء الصناعي</h6>
                            <div class="mb-3">
                                <label class="form-label">مفتاح API الحالي</label>
                                <select class="form-select" id="currentApiKey">
                                    <!-- API keys will be populated here -->
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عدد الطلبات المتزامنة</label>
                                <input type="number" class="form-control" id="concurrentRequests" value="3" min="1" max="5">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>إعدادات التصدير</h6>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeTimestamp" checked>
                                    <label class="form-check-label">تضمين الطابع الزمني</label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">تنسيق التاريخ</label>
                                <select class="form-select" id="dateFormat">
                                    <option value="ar">عربي</option>
                                    <option value="en">إنجليزي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="settings-actions">
                        <button class="btn btn-primary" id="exportSettings">
                            <i class="bi bi-download"></i> تصدير الإعدادات
                        </button>
                        <button class="btn btn-secondary" id="importSettings">
                            <i class="bi bi-upload"></i> استيراد الإعدادات
                        </button>
                        <input type="file" id="settingsFile" accept=".json" hidden>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- External Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <!-- Custom JS -->
    <script src="js/utils.js"></script>
    <script src="js/aiIntegration.js"></script>
    <script src="js/fileProcessor.js"></script>
    <script src="js/advancedProcessor.js"></script>
    <script src="js/exportUtils.js"></script>
    <script src="js/advancedForms.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
