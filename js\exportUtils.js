// ===== أدوات التصدير =====

class ExportUtils {
    constructor() {
        this.currentResults = null;
        this.currentResultsType = null;
    }

    // تعيين النتائج الحالية
    setCurrentResults(results, type) {
        this.currentResults = results;
        this.currentResultsType = type;
    }

    // تصدير إلى HTML
    exportToHtml() {
        if (!this.currentResults) {
            Utils.showAlert('لا توجد نتائج للتصدير', 'warning');
            return;
        }

        const htmlContent = this.generateHtmlReport();
        const filename = `تقرير_${this.currentResultsType}_${new Date().getTime()}.html`;
        Utils.downloadFile(htmlContent, filename, 'text/html');
        Utils.showAlert('تم تصدير التقرير بصيغة HTML', 'success');
    }

    // إنشاء تقرير HTML
    generateHtmlReport() {
        const timestamp = Utils.formatDate(new Date());
        
        return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير معالجة الملفات - ${this.currentResultsType}</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .search-box {
            margin-bottom: 2rem;
            position: relative;
        }
        
        .search-box input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            font-family: inherit;
        }
        
        .search-box input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .results-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: right;
            font-weight: 600;
        }
        
        .results-table td {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            text-align: right;
        }
        
        .results-table tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }
        
        .results-table tr:last-child td {
            border-bottom: none;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 1rem 2rem;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }
        
        .badge {
            padding: 0.3rem 0.6rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .badge-success {
            background: #28a745;
            color: white;
        }
        
        .badge-danger {
            background: #dc3545;
            color: white;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .stats {
                grid-template-columns: 1fr;
            }
            
            .results-table {
                font-size: 0.9rem;
            }
            
            .results-table th,
            .results-table td {
                padding: 0.5rem;
            }
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                border-radius: 0;
            }
            
            .search-box {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>تقرير معالجة الملفات</h1>
            <p>نوع التقرير: ${this.currentResultsType} • تاريخ الإنشاء: ${timestamp}</p>
        </div>
        
        <div class="content">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="البحث في النتائج..." onkeyup="searchTable()">
            </div>
            
            ${this.generateHtmlStats()}
            ${this.generateHtmlTable()}
        </div>
        
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة أداة معالجة ملفات النصوص المتقدمة</p>
        </div>
    </div>
    
    <script>
        function searchTable() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toLowerCase();
            const table = document.querySelector('.results-table');
            const rows = table.getElementsByTagName('tr');
            
            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length; j++) {
                    if (cells[j].textContent.toLowerCase().includes(filter)) {
                        found = true;
                        break;
                    }
                }
                
                row.style.display = found ? '' : 'none';
            }
        }
        
        // طباعة التقرير
        function printReport() {
            window.print();
        }
        
        // تحديد الكل
        function selectAll() {
            const range = document.createRange();
            range.selectNode(document.querySelector('.results-table'));
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
        }
    </script>
</body>
</html>
        `;
    }

    // إنشاء إحصائيات HTML
    generateHtmlStats() {
        if (!this.currentResults || !Array.isArray(this.currentResults)) {
            return '';
        }

        const totalFiles = this.currentResults.length;
        const successfulFiles = this.currentResults.filter(r => r.success !== false).length;
        const failedFiles = totalFiles - successfulFiles;

        return `
            <div class="stats">
                <div class="stat-card">
                    <span class="stat-number">${totalFiles}</span>
                    <span class="stat-label">إجمالي الملفات</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${successfulFiles}</span>
                    <span class="stat-label">تمت معالجتها بنجاح</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${failedFiles}</span>
                    <span class="stat-label">فشلت في المعالجة</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${Math.round((successfulFiles / totalFiles) * 100)}%</span>
                    <span class="stat-label">معدل النجاح</span>
                </div>
            </div>
        `;
    }

    // إنشاء جدول HTML
    generateHtmlTable() {
        if (!this.currentResults || !Array.isArray(this.currentResults)) {
            return '<p>لا توجد نتائج للعرض</p>';
        }

        const headers = this.getTableHeaders();
        const rows = this.currentResults.map(result => this.generateTableRow(result)).join('');

        return `
            <div class="table-responsive">
                <table class="results-table">
                    <thead>
                        <tr>
                            ${headers.map(header => `<th>${header}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${rows}
                    </tbody>
                </table>
            </div>
        `;
    }

    // الحصول على رؤوس الجدول
    getTableHeaders() {
        switch (this.currentResultsType) {
            case 'analysis':
                return ['اسم الملف', 'الموضوع', 'نوع النص', 'اللغة', 'مستوى الصعوبة', 'الكلمات المفتاحية', 'تقييم الجودة'];
            case 'lineCount':
                return ['اسم الملف', 'عدد الأسطر', 'عدد الكلمات', 'عدد الأحرف', 'الأسطر الفارغة'];
            case 'replace':
                return ['اسم الملف', 'عدد الاستبدالات', 'الحالة'];
            default:
                return ['اسم الملف', 'النتيجة', 'الحالة'];
        }
    }

    // إنشاء صف في الجدول
    generateTableRow(result) {
        switch (this.currentResultsType) {
            case 'analysis':
                return `
                    <tr>
                        <td><strong>${result.filename || 'غير محدد'}</strong></td>
                        <td>${result.success ? (result.result?.topic || 'غير محدد') : 'خطأ'}</td>
                        <td>${result.success ? (result.result?.type || 'غير محدد') : '-'}</td>
                        <td>${result.success ? (result.result?.language || 'غير محدد') : '-'}</td>
                        <td>${result.success ? (result.result?.difficulty || 'غير محدد') : '-'}</td>
                        <td>${result.success ? (result.result?.keywords?.slice(0, 5).join(', ') || 'لا توجد') : '-'}</td>
                        <td>${result.success ? (result.result?.quality || 'غير محدد') : (result.error || 'خطأ')}</td>
                    </tr>
                `;
            case 'lineCount':
                return `
                    <tr>
                        <td><strong>${result.filename || 'غير محدد'}</strong></td>
                        <td>${Utils.formatNumber(result.lines || 0)}</td>
                        <td>${Utils.formatNumber(result.words || 0)}</td>
                        <td>${Utils.formatNumber(result.characters || 0)}</td>
                        <td>${Utils.formatNumber(result.emptyLines || 0)}</td>
                    </tr>
                `;
            case 'replace':
                return `
                    <tr>
                        <td><strong>${result.filename || 'غير محدد'}</strong></td>
                        <td>${result.success ? Utils.formatNumber(result.replacements || 0) : '-'}</td>
                        <td>
                            ${result.success ?
                                '<span class="badge badge-success">نجح</span>' :
                                '<span class="badge badge-danger">فشل</span>'
                            }
                        </td>
                    </tr>
                `;
            default:
                return `
                    <tr>
                        <td><strong>${result.filename || 'غير محدد'}</strong></td>
                        <td>${result.result || result.error || 'غير محدد'}</td>
                        <td>
                            ${result.success !== false ?
                                '<span class="badge badge-success">نجح</span>' :
                                '<span class="badge badge-danger">فشل</span>'
                            }
                        </td>
                    </tr>
                `;
        }
    }

    // تصدير إلى Excel
    exportToExcel() {
        if (!this.currentResults) {
            Utils.showAlert('لا توجد نتائج للتصدير', 'warning');
            return;
        }

        const csvContent = this.generateCsvContent();
        const filename = `تقرير_${this.currentResultsType}_${new Date().getTime()}.csv`;
        
        // إضافة BOM للدعم العربي في Excel
        const bom = '\uFEFF';
        Utils.downloadFile(bom + csvContent, filename, 'text/csv;charset=utf-8');
        Utils.showAlert('تم تصدير التقرير بصيغة Excel', 'success');
    }

    // إنشاء محتوى CSV
    generateCsvContent() {
        const headers = this.getTableHeaders();
        const rows = this.currentResults.map(result => this.generateCsvRow(result));
        
        return [
            headers.join(','),
            ...rows
        ].join('\n');
    }

    // إنشاء صف CSV
    generateCsvRow(result) {
        const escapeCSV = (text) => {
            if (typeof text !== 'string') text = String(text);
            return `"${text.replace(/"/g, '""')}"`;
        };

        switch (this.currentResultsType) {
            case 'analysis':
                return [
                    escapeCSV(result.filename),
                    escapeCSV(result.success ? result.result.topic : 'خطأ'),
                    escapeCSV(result.success ? result.result.type : '-'),
                    escapeCSV(result.success ? result.result.language : '-'),
                    escapeCSV(result.success ? result.result.difficulty : '-'),
                    escapeCSV(result.success ? result.result.keywords.join(', ') : '-'),
                    escapeCSV(result.success ? result.result.quality : result.error || 'خطأ')
                ].join(',');
            case 'lineCount':
                return [
                    escapeCSV(result.filename),
                    escapeCSV(result.lines),
                    escapeCSV(result.words),
                    escapeCSV(result.characters),
                    escapeCSV(result.emptyLines)
                ].join(',');
            case 'replace':
                return [
                    escapeCSV(result.filename),
                    escapeCSV(result.success ? result.replacements : '-'),
                    escapeCSV(result.success ? 'نجح' : 'فشل')
                ].join(',');
            default:
                return [
                    escapeCSV(result.filename),
                    escapeCSV(result.result || result.error || 'غير محدد'),
                    escapeCSV(result.success !== false ? 'نجح' : 'فشل')
                ].join(',');
        }
    }

    // تصدير إلى PDF
    async exportToPdf() {
        if (!this.currentResults) {
            Utils.showAlert('لا توجد نتائج للتصدير', 'warning');
            return;
        }

        try {
            // استخدام مكتبة jsPDF لإنشاء PDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF({
                orientation: 'landscape',
                unit: 'mm',
                format: 'a4'
            });

            // إعداد الخط العربي
            doc.setFont('helvetica');
            doc.setFontSize(16);
            
            // العنوان
            doc.text(`تقرير ${this.currentResultsType}`, 20, 20);
            doc.setFontSize(12);
            doc.text(`تاريخ الإنشاء: ${Utils.formatDate(new Date())}`, 20, 30);

            // الجدول
            const headers = this.getTableHeaders();
            const rows = this.currentResults.map(result => this.generatePdfRow(result));

            doc.autoTable({
                head: [headers],
                body: rows,
                startY: 40,
                styles: {
                    font: 'helvetica',
                    fontSize: 10,
                    cellPadding: 3
                },
                headStyles: {
                    fillColor: [102, 126, 234],
                    textColor: 255
                }
            });

            const filename = `تقرير_${this.currentResultsType}_${new Date().getTime()}.pdf`;
            doc.save(filename);
            Utils.showAlert('تم تصدير التقرير بصيغة PDF', 'success');

        } catch (error) {
            console.error('خطأ في تصدير PDF:', error);
            Utils.showAlert('فشل في تصدير PDF. يرجى المحاولة مرة أخرى.', 'danger');
        }
    }

    // إنشاء صف PDF
    generatePdfRow(result) {
        switch (this.currentResultsType) {
            case 'analysis':
                return [
                    result.filename,
                    result.success ? result.result.topic : 'خطأ',
                    result.success ? result.result.type : '-',
                    result.success ? result.result.language : '-',
                    result.success ? result.result.difficulty : '-',
                    result.success ? result.result.keywords.slice(0, 3).join(', ') : '-',
                    result.success ? result.result.quality : 'خطأ'
                ];
            case 'lineCount':
                return [
                    result.filename,
                    result.lines.toString(),
                    result.words.toString(),
                    result.characters.toString(),
                    result.emptyLines.toString()
                ];
            case 'replace':
                return [
                    result.filename,
                    result.success ? result.replacements.toString() : '-',
                    result.success ? 'نجح' : 'فشل'
                ];
            default:
                return [
                    result.filename,
                    result.result || result.error || 'غير محدد',
                    result.success !== false ? 'نجح' : 'فشل'
                ];
        }
    }

    // تصدير إلى ZIP
    async exportToZip() {
        if (!this.currentResults) {
            Utils.showAlert('لا توجد نتائج للتصدير', 'warning');
            return;
        }

        try {
            const JSZip = window.JSZip;
            const zip = new JSZip();

            // إضافة تقرير HTML
            const htmlContent = this.generateHtmlReport();
            zip.file(`تقرير_${this.currentResultsType}.html`, htmlContent);

            // إضافة تقرير CSV
            const csvContent = this.generateCsvContent();
            zip.file(`تقرير_${this.currentResultsType}.csv`, '\uFEFF' + csvContent);

            // إضافة ملف JSON للبيانات الخام
            const jsonContent = JSON.stringify(this.currentResults, null, 2);
            zip.file(`بيانات_${this.currentResultsType}.json`, jsonContent);

            // إضافة ملف README
            const readmeContent = this.generateReadmeContent();
            zip.file('README.txt', readmeContent);

            // إنشاء وتحميل الملف المضغوط
            const content = await zip.generateAsync({ type: 'blob' });
            const filename = `تقرير_شامل_${this.currentResultsType}_${new Date().getTime()}.zip`;
            
            const url = URL.createObjectURL(content);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            Utils.showAlert('تم تصدير الحزمة الشاملة بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير ZIP:', error);
            Utils.showAlert('فشل في تصدير الحزمة المضغوطة', 'danger');
        }
    }

    // إنشاء محتوى README
    generateReadmeContent() {
        const timestamp = Utils.formatDate(new Date());
        
        return `
تقرير معالجة ملفات النصوص
==========================

نوع التقرير: ${this.currentResultsType}
تاريخ الإنشاء: ${timestamp}
عدد الملفات: ${this.currentResults ? this.currentResults.length : 0}

محتويات الحزمة:
================

1. تقرير_${this.currentResultsType}.html
   - تقرير تفاعلي بصيغة HTML مع إمكانية البحث والطباعة

2. تقرير_${this.currentResultsType}.csv
   - بيانات التقرير بصيغة CSV قابلة للاستيراد في Excel

3. بيانات_${this.currentResultsType}.json
   - البيانات الخام بصيغة JSON للمطورين

4. README.txt
   - هذا الملف التوضيحي

تم إنشاء هذا التقرير بواسطة:
أداة معالجة ملفات النصوص المتقدمة
        `;
    }

    // تصدير الإعدادات
    exportSettings() {
        const settings = {
            apiKeyIndex: fileProcessor.aiIntegration.currentKeyIndex,
            dateFormat: document.getElementById('dateFormat')?.value || 'ar',
            includeTimestamp: document.getElementById('includeTimestamp')?.checked || true,
            concurrentRequests: document.getElementById('concurrentRequests')?.value || 3,
            exportedAt: new Date().toISOString()
        };

        const filename = `إعدادات_أداة_النصوص_${new Date().getTime()}.json`;
        Utils.downloadFile(JSON.stringify(settings, null, 2), filename, 'application/json');
        Utils.showAlert('تم تصدير الإعدادات بنجاح', 'success');
    }

    // استيراد الإعدادات
    async importSettings(file) {
        try {
            const content = await fileProcessor.readFileContent(file);
            const settings = JSON.parse(content);

            // تطبيق الإعدادات
            if (settings.apiKeyIndex !== undefined) {
                fileProcessor.aiIntegration.currentKeyIndex = settings.apiKeyIndex;
            }
            
            if (settings.dateFormat && document.getElementById('dateFormat')) {
                document.getElementById('dateFormat').value = settings.dateFormat;
            }
            
            if (settings.includeTimestamp !== undefined && document.getElementById('includeTimestamp')) {
                document.getElementById('includeTimestamp').checked = settings.includeTimestamp;
            }
            
            if (settings.concurrentRequests && document.getElementById('concurrentRequests')) {
                document.getElementById('concurrentRequests').value = settings.concurrentRequests;
            }

            Utils.showAlert('تم استيراد الإعدادات بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في استيراد الإعدادات:', error);
            Utils.showAlert('فشل في استيراد الإعدادات. تأكد من صحة الملف.', 'danger');
        }
    }
}

// إنشاء مثيل عام من أدوات التصدير
const exportUtils = new ExportUtils();
