// ===== تكامل الذكاء الصناعي مع Gemini API =====

class AIIntegration {
    constructor() {
        this.apiKeys = [
            'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
            'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
            'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
            'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
            'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc',
            'AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM',
            'AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds',
            'AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk',
            'AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg',
            'AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ',
            'AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4',
            'AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo',
            'AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k',
            'AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA',
            'AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ'
        ];
        this.currentKeyIndex = 0;
        this.maxTokensPerRequest = 30000;
        this.requestDelay = 1000; // تأخير بين الطلبات
        this.maxRetries = 3;
    }

    // الحصول على مفتاح API الحالي
    getCurrentApiKey() {
        return this.apiKeys[this.currentKeyIndex];
    }

    // التبديل إلى مفتاح API التالي
    switchToNextApiKey() {
        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
        console.log(`تم التبديل إلى مفتاح API رقم: ${this.currentKeyIndex + 1}`);
    }

    // تقسيم النص إلى أجزاء صغيرة
    splitTextIntoChunks(text, maxChunkSize = 25000) {
        const chunks = [];
        const lines = text.split('\n');
        let currentChunk = '';

        for (const line of lines) {
            if ((currentChunk + line + '\n').length > maxChunkSize && currentChunk.length > 0) {
                chunks.push(currentChunk.trim());
                currentChunk = line + '\n';
            } else {
                currentChunk += line + '\n';
            }
        }

        if (currentChunk.trim().length > 0) {
            chunks.push(currentChunk.trim());
        }

        return chunks;
    }

    // إرسال طلب إلى Gemini API
    async makeApiRequest(prompt, retryCount = 0) {
        const apiKey = this.getCurrentApiKey();
        const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{ text: prompt }]
                    }],
                    generationConfig: {
                        temperature: 0.7,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 8192,
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                return data.candidates[0].content.parts[0].text;
            } else {
                throw new Error('استجابة غير صالحة من API');
            }

        } catch (error) {
            console.error('خطأ في API:', error);
            
            if (retryCount < this.maxRetries) {
                console.log(`إعادة المحاولة ${retryCount + 1}/${this.maxRetries}`);
                this.switchToNextApiKey();
                await Utils.delay(this.requestDelay * (retryCount + 1));
                return this.makeApiRequest(prompt, retryCount + 1);
            } else {
                throw new Error(`فشل في الاتصال بـ API بعد ${this.maxRetries} محاولات`);
            }
        }
    }

    // تحليل ملف نصي بالذكاء الصناعي
    async analyzeFile(filename, content) {
        const prompt = `
قم بتحليل الملف النصي التالي بشكل احترافي ومفصل:

اسم الملف: ${filename}

المحتوى:
${content}

يرجى تقديم تحليل شامل يتضمن:
1. موضوع المحتوى الرئيسي
2. نوع النص (مقال، قصة، تقرير، إلخ)
3. اللغة المستخدمة ومستوى الصعوبة
4. الكلمات المفتاحية الأساسية (5-10 كلمات)
5. ملخص مختصر (2-3 جمل)
6. تقييم جودة المحتوى
7. اقتراحات للتحسين
8. الفئة المناسبة للمحتوى

قدم الإجابة بتنسيق JSON مع المفاتيح التالية:
{
  "topic": "الموضوع الرئيسي",
  "type": "نوع النص",
  "language": "اللغة",
  "difficulty": "مستوى الصعوبة",
  "keywords": ["كلمة1", "كلمة2", "..."],
  "summary": "الملخص",
  "quality": "تقييم الجودة",
  "suggestions": "اقتراحات التحسين",
  "category": "الفئة"
}
`;

        try {
            const response = await this.makeApiRequest(prompt);
            return JSON.parse(response);
        } catch (error) {
            console.error('خطأ في تحليل الملف:', error);
            return {
                topic: 'غير محدد',
                type: 'نص عام',
                language: 'عربي',
                difficulty: 'متوسط',
                keywords: [],
                summary: 'لم يتم إنشاء ملخص',
                quality: 'غير محدد',
                suggestions: 'لا توجد اقتراحات',
                category: 'عام'
            };
        }
    }

    // إنشاء اسم ملف مناسب بناءً على المحتوى
    async generateFilename(content, maxLength = 50) {
        const prompt = `
قم بإنشاء اسم ملف مناسب ووصفي للمحتوى التالي:

${content.substring(0, 1000)}...

المتطلبات:
- الاسم يجب أن يكون وصفياً ومفهوماً
- لا يتجاوز ${maxLength} حرف
- يستخدم الأحرف العربية والإنجليزية والأرقام فقط
- لا يحتوي على رموز خاصة أو مسافات (استخدم _ أو -)
- يعكس محتوى النص بدقة

قدم فقط اسم الملف بدون أي تفسيرات إضافية.
`;

        try {
            const response = await this.makeApiRequest(prompt);
            return response.trim().replace(/[^a-zA-Z0-9\u0600-\u06FF_-]/g, '_').substring(0, maxLength);
        } catch (error) {
            console.error('خطأ في إنشاء اسم الملف:', error);
            return `ملف_${Date.now()}`;
        }
    }

    // تحسين النص
    async improveText(content, improvementType = 'general') {
        const prompts = {
            general: `قم بتحسين النص التالي من ناحية الوضوح والأسلوب والقواعد النحوية:`,
            grammar: `قم بتصحيح الأخطاء النحوية والإملائية في النص التالي:`,
            style: `قم بتحسين أسلوب الكتابة في النص التالي ليصبح أكثر جاذبية:`,
            formal: `قم بتحويل النص التالي إلى أسلوب رسمي:`,
            simple: `قم بتبسيط النص التالي ليصبح أسهل فهماً:`
        };

        const prompt = `
${prompts[improvementType] || prompts.general}

${content}

قدم النص المحسن فقط بدون أي تفسيرات إضافية.
`;

        try {
            return await this.makeApiRequest(prompt);
        } catch (error) {
            console.error('خطأ في تحسين النص:', error);
            return content;
        }
    }

    // إنشاء ملخص للنص
    async summarizeText(content, summaryLength = 'medium') {
        const lengths = {
            short: 'ملخص قصير في 2-3 جمل',
            medium: 'ملخص متوسط في 5-7 جمل',
            long: 'ملخص مفصل في 10-15 جملة'
        };

        const prompt = `
قم بإنشاء ${lengths[summaryLength] || lengths.medium} للنص التالي:

${content}

يجب أن يكون الملخص:
- شاملاً للنقاط الرئيسية
- واضحاً ومفهوماً
- محافظاً على المعنى الأصلي
- مكتوباً بأسلوب سلس

قدم الملخص فقط بدون أي تفسيرات إضافية.
`;

        try {
            return await this.makeApiRequest(prompt);
        } catch (error) {
            console.error('خطأ في إنشاء الملخص:', error);
            return 'لم يتم إنشاء ملخص';
        }
    }

    // تحويل النص إلى HTML مع تنسيق جميل
    async convertToHtml(content, title = 'مستند') {
        const prompt = `
قم بتحويل النص التالي إلى صفحة HTML جميلة ومنسقة:

العنوان: ${title}
المحتوى:
${content}

المتطلبات:
- استخدم HTML5 مع CSS مدمج
- تصميم جميل ومتجاوب
- خطوط عربية مناسبة
- ألوان متناسقة
- تنسيق الفقرات والعناوين
- إضافة تأثيرات بصرية بسيطة
- دعم الاتجاه من اليمين لليسار (RTL)

قدم كود HTML الكامل فقط.
`;

        try {
            return await this.makeApiRequest(prompt);
        } catch (error) {
            console.error('خطأ في تحويل إلى HTML:', error);
            return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        body { font-family: 'Cairo', sans-serif; margin: 40px; line-height: 1.6; }
        h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        p { margin-bottom: 15px; }
    </style>
</head>
<body>
    <h1>${title}</h1>
    <div>${content.replace(/\n/g, '<br>')}</div>
</body>
</html>
`;
        }
    }

    // استخراج الكلمات المفتاحية
    async extractKeywords(content, count = 10) {
        const prompt = `
استخرج أهم ${count} كلمات مفتاحية من النص التالي:

${content}

المتطلبات:
- الكلمات يجب أن تكون ذات معنى ومفيدة
- تجنب الكلمات الشائعة جداً (مثل: في، من، إلى)
- رتب الكلمات حسب الأهمية
- قدم النتيجة كقائمة مرقمة

قدم الكلمات المفتاحية فقط بدون تفسيرات.
`;

        try {
            const response = await this.makeApiRequest(prompt);
            return response.split('\n').filter(line => line.trim().length > 0);
        } catch (error) {
            console.error('خطأ في استخراج الكلمات المفتاحية:', error);
            return [];
        }
    }

    // معالجة متعددة للملفات
    async processMultipleFiles(files, operation, options = {}) {
        const results = [];
        const totalFiles = files.length;
        
        for (let i = 0; i < totalFiles; i++) {
            const file = files[i];
            Utils.updateProgress((i / totalFiles) * 100, `معالجة الملف ${i + 1} من ${totalFiles}: ${file.name}`);
            
            try {
                let result;
                switch (operation) {
                    case 'analyze':
                        result = await this.analyzeFile(file.name, file.content);
                        break;
                    case 'summarize':
                        result = await this.summarizeText(file.content, options.summaryLength);
                        break;
                    case 'improve':
                        result = await this.improveText(file.content, options.improvementType);
                        break;
                    case 'generateFilename':
                        result = await this.generateFilename(file.content, options.maxLength);
                        break;
                    case 'convertToHtml':
                        result = await this.convertToHtml(file.content, file.name);
                        break;
                    case 'extractKeywords':
                        result = await this.extractKeywords(file.content, options.count);
                        break;
                    default:
                        result = 'عملية غير مدعومة';
                }
                
                results.push({
                    filename: file.name,
                    success: true,
                    result: result
                });
                
            } catch (error) {
                console.error(`خطأ في معالجة الملف ${file.name}:`, error);
                results.push({
                    filename: file.name,
                    success: false,
                    error: error.message
                });
            }
            
            // تأخير بين الطلبات لتجنب تجاوز الحدود
            if (i < totalFiles - 1) {
                await Utils.delay(this.requestDelay);
            }
        }
        
        Utils.updateProgress(100, 'تم الانتهاء من معالجة جميع الملفات');
        return results;
    }

    // تحليل مجموعة من الملفات وإنشاء تقرير شامل
    async generateComprehensiveReport(files) {
        const analyses = await this.processMultipleFiles(files, 'analyze');
        
        const prompt = `
بناءً على تحليل الملفات التالية، قم بإنشاء تقرير شامل:

${JSON.stringify(analyses, null, 2)}

يجب أن يتضمن التقرير:
1. نظرة عامة على المجموعة
2. الموضوعات الرئيسية المشتركة
3. توزيع أنواع المحتوى
4. تحليل الجودة العامة
5. التوصيات والاقتراحات
6. إحصائيات مفيدة

قدم التقرير بتنسيق منظم وواضح.
`;

        try {
            return await this.makeApiRequest(prompt);
        } catch (error) {
            console.error('خطأ في إنشاء التقرير الشامل:', error);
            return 'لم يتم إنشاء التقرير الشامل';
        }
    }
}
