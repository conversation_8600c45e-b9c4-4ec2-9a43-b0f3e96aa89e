// ===== وظائف مساعدة عامة =====

class Utils {
    // تنسيق حجم الملف
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // تنسيق التاريخ
    static formatDate(date, format = 'ar') {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        if (format === 'ar') {
            return new Intl.DateTimeFormat('ar-SA', options).format(date);
        } else {
            return new Intl.DateTimeFormat('en-US', options).format(date);
        }
    }

    // تنسيق الأرقام
    static formatNumber(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    }

    // إنشاء معرف فريد
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // تنظيف النص
    static cleanText(text) {
        return text.trim().replace(/\s+/g, ' ');
    }

    // تحويل النص إلى HTML آمن
    static escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // إزالة HTML من النص
    static stripHtml(html) {
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    }

    // تحديد نوع الملف
    static getFileType(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        const types = {
            'txt': 'نص',
            'doc': 'مستند Word',
            'docx': 'مستند Word',
            'pdf': 'PDF',
            'html': 'صفحة ويب',
            'htm': 'صفحة ويب'
        };
        return types[extension] || 'غير معروف';
    }

    // تحليل محتوى النص
    static analyzeText(text) {
        const lines = text.split('\n');
        const words = text.split(/\s+/).filter(word => word.length > 0);
        const characters = text.length;
        const charactersNoSpaces = text.replace(/\s/g, '').length;
        const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
        const emptyLines = lines.filter(line => line.trim() === '').length;

        return {
            lines: lines.length,
            words: words.length,
            characters: characters,
            charactersNoSpaces: charactersNoSpaces,
            paragraphs: paragraphs.length,
            emptyLines: emptyLines,
            averageWordsPerLine: Math.round(words.length / lines.length * 100) / 100,
            averageCharsPerLine: Math.round(characters / lines.length * 100) / 100
        };
    }

    // البحث في النص
    static searchInText(text, query, caseSensitive = false) {
        if (!query) return [];
        
        const searchText = caseSensitive ? text : text.toLowerCase();
        const searchQuery = caseSensitive ? query : query.toLowerCase();
        const lines = text.split('\n');
        const results = [];

        lines.forEach((line, index) => {
            const searchLine = caseSensitive ? line : line.toLowerCase();
            if (searchLine.includes(searchQuery)) {
                results.push({
                    lineNumber: index + 1,
                    content: line,
                    position: searchLine.indexOf(searchQuery)
                });
            }
        });

        return results;
    }

    // تحويل النص إلى مصفوفة كلمات
    static textToWords(text) {
        return text.split(/\s+/).filter(word => word.length > 0);
    }

    // إحصائيات الكلمات
    static getWordFrequency(text) {
        const words = this.textToWords(text.toLowerCase());
        const frequency = {};
        
        words.forEach(word => {
            // إزالة علامات الترقيم
            const cleanWord = word.replace(/[^\u0600-\u06FFa-zA-Z0-9]/g, '');
            if (cleanWord.length > 0) {
                frequency[cleanWord] = (frequency[cleanWord] || 0) + 1;
            }
        });

        return Object.entries(frequency)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 50); // أكثر 50 كلمة تكراراً
    }

    // تحديد اللغة الأساسية للنص
    static detectLanguage(text) {
        const arabicChars = (text.match(/[\u0600-\u06FF]/g) || []).length;
        const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
        const totalChars = arabicChars + englishChars;
        
        if (totalChars === 0) return 'غير محدد';
        
        const arabicPercentage = (arabicChars / totalChars) * 100;
        
        if (arabicPercentage > 60) return 'عربي';
        if (arabicPercentage < 20) return 'إنجليزي';
        return 'مختلط';
    }

    // تحويل الترميز
    static convertEncoding(text, fromEncoding = 'utf-8', toEncoding = 'utf-8') {
        // هذه وظيفة أساسية - يمكن تطويرها لاحقاً
        return text;
    }

    // تنظيف النص من الأحرف الخاصة
    static removeSpecialChars(text, keepSpaces = true) {
        if (keepSpaces) {
            return text.replace(/[^\u0600-\u06FFa-zA-Z0-9\s]/g, '');
        } else {
            return text.replace(/[^\u0600-\u06FFa-zA-Z0-9]/g, '');
        }
    }

    // تحويل الأرقام الإنجليزية إلى عربية
    static convertNumbersToArabic(text) {
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        
        let result = text;
        englishNumbers.forEach((num, index) => {
            result = result.replace(new RegExp(num, 'g'), arabicNumbers[index]);
        });
        
        return result;
    }

    // تحويل الأرقام العربية إلى إنجليزية
    static convertNumbersToEnglish(text) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        
        let result = text;
        arabicNumbers.forEach((num, index) => {
            result = result.replace(new RegExp(num, 'g'), englishNumbers[index]);
        });
        
        return result;
    }

    // تحديد نمط النص (رسمي، غير رسمي، إلخ)
    static detectTextStyle(text) {
        const formalWords = ['حضرتكم', 'سيادتكم', 'المحترم', 'المحترمة', 'تفضلوا', 'يشرفني'];
        const informalWords = ['هاي', 'مرحبا', 'كيفك', 'شلونك', 'اهلين'];
        
        const formalCount = formalWords.reduce((count, word) => {
            return count + (text.includes(word) ? 1 : 0);
        }, 0);
        
        const informalCount = informalWords.reduce((count, word) => {
            return count + (text.includes(word) ? 1 : 0);
        }, 0);
        
        if (formalCount > informalCount) return 'رسمي';
        if (informalCount > formalCount) return 'غير رسمي';
        return 'محايد';
    }

    // تحويل النص إلى عنوان URL صالح
    static slugify(text) {
        return text
            .toLowerCase()
            .replace(/[\u0600-\u06FF]/g, '') // إزالة الأحرف العربية
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }

    // تحديد مستوى صعوبة النص
    static getTextComplexity(text) {
        const words = this.textToWords(text);
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const avgWordsPerSentence = words.length / sentences.length;
        const avgCharsPerWord = text.replace(/\s/g, '').length / words.length;
        
        let complexity = 'بسيط';
        if (avgWordsPerSentence > 15 || avgCharsPerWord > 6) {
            complexity = 'متوسط';
        }
        if (avgWordsPerSentence > 25 || avgCharsPerWord > 8) {
            complexity = 'معقد';
        }
        
        return {
            level: complexity,
            avgWordsPerSentence: Math.round(avgWordsPerSentence * 100) / 100,
            avgCharsPerWord: Math.round(avgCharsPerWord * 100) / 100
        };
    }

    // تحويل النص إلى مصفوفة جمل
    static textToSentences(text) {
        return text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
    }

    // إنشاء ملخص سريع للنص
    static generateQuickSummary(text, maxLength = 200) {
        const sentences = this.textToSentences(text);
        let summary = '';
        
        for (const sentence of sentences) {
            if ((summary + sentence).length <= maxLength) {
                summary += sentence.trim() + '. ';
            } else {
                break;
            }
        }
        
        return summary.trim() || text.substring(0, maxLength) + '...';
    }

    // تحديد الكلمات المفتاحية
    static extractKeywords(text, count = 10) {
        const frequency = this.getWordFrequency(text);
        const commonWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'التي', 'الذي', 'أن', 'أو', 'لا', 'ما', 'كان', 'كانت'];
        
        return frequency
            .filter(([word]) => word.length > 2 && !commonWords.includes(word))
            .slice(0, count)
            .map(([word, freq]) => ({ word, frequency: freq }));
    }

    // تحويل البيانات إلى CSV
    static arrayToCSV(data, headers) {
        const csvContent = [
            headers.join(','),
            ...data.map(row => row.map(cell => `"${cell}"`).join(','))
        ].join('\n');
        
        return csvContent;
    }

    // تحميل ملف
    static downloadFile(content, filename, type = 'text/plain') {
        const blob = new Blob([content], { type: type });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // عرض رسالة تنبيه
    static showAlert(message, type = 'info', duration = 5000) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} fade-in-up`;
        alertDiv.innerHTML = `
            <i class="bi bi-${this.getAlertIcon(type)}"></i>
            ${message}
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            alertDiv.remove();
        }, duration);
    }

    // الحصول على أيقونة التنبيه
    static getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'danger': 'x-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // تحديث شريط التقدم
    static updateProgress(percentage, text = '') {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const progressPercent = document.getElementById('progressPercent');
        
        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }
        
        if (progressText && text) {
            progressText.textContent = text;
        }
        
        if (progressPercent) {
            progressPercent.textContent = Math.round(percentage) + '%';
        }
    }

    // تأخير التنفيذ
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // تحديد ما إذا كان الجهاز محمول
    static isMobile() {
        return window.innerWidth <= 768;
    }

    // نسخ النص إلى الحافظة
    static async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showAlert('تم نسخ النص إلى الحافظة', 'success', 2000);
        } catch (err) {
            console.error('فشل في نسخ النص:', err);
            this.showAlert('فشل في نسخ النص', 'danger', 2000);
        }
    }
}
