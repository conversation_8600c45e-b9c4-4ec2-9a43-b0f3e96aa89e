/* ===== أساسيات التصميم ===== */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    direction: rtl;
    line-height: 1.6;
    color: var(--dark-color);
}

/* ===== Header ===== */
.main-header {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    box-shadow: var(--shadow-medium);
    position: relative;
    overflow: hidden;
}

.main-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.header-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.header-title i {
    margin-left: 1rem;
    color: #ffd700;
}

.header-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* ===== Main Content ===== */
.main-content {
    padding: 3rem 0;
}

.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: none;
    margin-bottom: 2rem;
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem;
    border-bottom: none;
}

.card-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.card-header i {
    margin-left: 0.5rem;
    color: #ffd700;
}

.card-body {
    padding: 2rem;
}

/* ===== Upload Area ===== */
.upload-area {
    border: 3px dashed var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 3rem;
    text-align: center;
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%), 
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    transition: var(--transition);
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--accent-color);
    background-color: rgba(52, 152, 219, 0.05);
}

.upload-area.dragover {
    border-color: var(--success-color);
    background-color: rgba(39, 174, 96, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    display: block;
}

.upload-content h4 {
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.upload-content p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* ===== Uploaded Files ===== */
.uploaded-files {
    margin-top: 2rem;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.file-item:hover {
    background: #e9ecef;
    transform: translateX(-5px);
}

.file-info {
    display: flex;
    align-items: center;
}

.file-info i {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-left: 1rem;
}

.file-details h6 {
    margin: 0;
    color: var(--dark-color);
    font-weight: 600;
}

.file-details small {
    color: #6c757d;
}

.file-actions button {
    margin-right: 0.5rem;
}

/* ===== Option Cards ===== */
.option-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.option-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: var(--transition);
}

.option-card:hover::before {
    left: 100%;
}

.option-card:hover {
    border-color: var(--secondary-color);
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.option-card.selected {
    border-color: var(--success-color);
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(39, 174, 96, 0.05) 100%);
}

.option-card i {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    display: block;
}

.option-card.selected i {
    color: var(--success-color);
}

.option-card h5 {
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.option-card p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

/* ===== Progress Section ===== */
.progress-container {
    text-align: center;
}

.progress {
    height: 20px;
    border-radius: 10px;
    background: #e9ecef;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    background: var(--gradient-primary);
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, .2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    font-weight: 600;
}

.progress-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* ===== Results Section ===== */
.search-container {
    position: relative;
}

.search-container::before {
    content: '\F52A';
    font-family: 'bootstrap-icons';
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 1;
}

#searchResults {
    padding-right: 3rem;
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

#searchResults:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.export-buttons {
    display: flex;
    gap: 0.5rem;
}

.export-buttons .btn {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .header-title {
        font-size: 2rem;
    }
    
    .header-subtitle {
        font-size: 1rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .upload-icon {
        font-size: 3rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .option-card {
        padding: 1rem;
    }
    
    .option-card i {
        font-size: 2rem;
    }
    
    .export-buttons {
        flex-wrap: wrap;
    }
    
    .progress-controls {
        flex-direction: column;
    }
}

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* ===== Utility Classes ===== */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.btn-gradient {
    background: var(--gradient-primary);
    border: none;
    color: white;
    transition: var(--transition);
}

.btn-gradient:hover {
    background: var(--gradient-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}
