// ===== معالج الملفات =====

class FileProcessor {
    constructor() {
        this.files = [];
        this.aiIntegration = new AIIntegration();
        this.processingCancelled = false;
        this.processingPaused = false;
    }

    // إضافة ملفات جديدة
    addFiles(fileList) {
        for (const file of fileList) {
            if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                this.files.push({
                    id: Utils.generateId(),
                    file: file,
                    name: file.name,
                    size: file.size,
                    content: null,
                    processed: false
                });
            }
        }
        this.updateFilesList();
    }

    // قراءة محتوى الملفات
    async readFiles() {
        const promises = this.files.map(async (fileObj) => {
            if (!fileObj.content) {
                try {
                    const text = await this.readFileContent(fileObj.file);
                    fileObj.content = text;
                    fileObj.stats = Utils.analyzeText(text);
                } catch (error) {
                    console.error(`خطأ في قراءة الملف ${fileObj.name}:`, error);
                    fileObj.error = error.message;
                }
            }
        });

        await Promise.all(promises);
        return this.files;
    }

    // قراءة محتوى ملف واحد
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('فشل في قراءة الملف'));
            reader.readAsText(file, 'UTF-8');
        });
    }

    // تحديث قائمة الملفات في الواجهة
    updateFilesList() {
        const container = document.getElementById('uploadedFiles');
        if (!container) return;

        if (this.files.length === 0) {
            container.innerHTML = '';
            return;
        }

        container.innerHTML = `
            <h5 class="mt-3 mb-3">الملفات المرفوعة (${this.files.length})</h5>
            <div class="files-list">
                ${this.files.map(fileObj => `
                    <div class="file-item" data-file-id="${fileObj.id}">
                        <div class="file-info">
                            <i class="bi bi-file-text"></i>
                            <div class="file-details">
                                <h6>${fileObj.name}</h6>
                                <small>${Utils.formatFileSize(fileObj.size)} • ${Utils.getFileType(fileObj.name)}</small>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="fileProcessor.previewFile('${fileObj.id}')">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="fileProcessor.removeFile('${fileObj.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        // إظهار قسم الخيارات
        document.getElementById('optionsSection').style.display = 'block';
    }

    // إزالة ملف
    removeFile(fileId) {
        this.files = this.files.filter(f => f.id !== fileId);
        this.updateFilesList();
        
        if (this.files.length === 0) {
            document.getElementById('optionsSection').style.display = 'none';
        }
    }

    // معاينة ملف
    async previewFile(fileId) {
        const fileObj = this.files.find(f => f.id === fileId);
        if (!fileObj) return;

        if (!fileObj.content) {
            try {
                fileObj.content = await this.readFileContent(fileObj.file);
            } catch (error) {
                Utils.showAlert('فشل في قراءة الملف', 'danger');
                return;
            }
        }

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">معاينة: ${fileObj.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="file-preview">
                            ${fileObj.content.split('\n').map((line, index) => 
                                `<div><span class="line-number">${index + 1}</span><span class="line-content">${Utils.escapeHtml(line)}</span></div>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    // تحليل تفصيلي للملفات
    async analyzeFiles() {
        await this.readFiles();
        this.showProgressSection();
        
        try {
            const results = await this.aiIntegration.processMultipleFiles(
                this.files.map(f => ({ name: f.name, content: f.content })),
                'analyze'
            );

            this.displayAnalysisResults(results);
        } catch (error) {
            Utils.showAlert('فشل في تحليل الملفات: ' + error.message, 'danger');
        } finally {
            this.hideProgressSection();
        }
    }

    // عرض نتائج التحليل
    displayAnalysisResults(results) {
        const resultsContent = document.getElementById('resultsContent');
        
        const tableHtml = `
            <div class="table-responsive">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>الموضوع</th>
                            <th>نوع النص</th>
                            <th>اللغة</th>
                            <th>مستوى الصعوبة</th>
                            <th>الكلمات المفتاحية</th>
                            <th>الملخص</th>
                            <th>تقييم الجودة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map(result => `
                            <tr>
                                <td><strong>${result.filename}</strong></td>
                                <td>${result.success ? result.result.topic : 'خطأ'}</td>
                                <td>${result.success ? result.result.type : '-'}</td>
                                <td>${result.success ? result.result.language : '-'}</td>
                                <td>${result.success ? result.result.difficulty : '-'}</td>
                                <td>${result.success ? result.result.keywords.slice(0, 3).join(', ') : '-'}</td>
                                <td>${result.success ? result.result.summary.substring(0, 100) + '...' : result.error || 'خطأ'}</td>
                                <td>${result.success ? result.result.quality : '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        resultsContent.innerHTML = tableHtml;
        this.showResultsSection();
    }

    // حساب عدد الأسطر
    async countLines() {
        await this.readFiles();
        
        const results = this.files.map(fileObj => ({
            filename: fileObj.name,
            lines: fileObj.stats ? fileObj.stats.lines : 0,
            words: fileObj.stats ? fileObj.stats.words : 0,
            characters: fileObj.stats ? fileObj.stats.characters : 0,
            emptyLines: fileObj.stats ? fileObj.stats.emptyLines : 0
        }));

        this.displayLineCountResults(results);
    }

    // عرض نتائج عد الأسطر
    displayLineCountResults(results) {
        const resultsContent = document.getElementById('resultsContent');
        
        const totalLines = results.reduce((sum, r) => sum + r.lines, 0);
        const totalWords = results.reduce((sum, r) => sum + r.words, 0);
        const totalChars = results.reduce((sum, r) => sum + r.characters, 0);

        const tableHtml = `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <span class="stat-number">${Utils.formatNumber(totalLines)}</span>
                        <span class="stat-label">إجمالي الأسطر</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <span class="stat-number">${Utils.formatNumber(totalWords)}</span>
                        <span class="stat-label">إجمالي الكلمات</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <span class="stat-number">${Utils.formatNumber(totalChars)}</span>
                        <span class="stat-label">إجمالي الأحرف</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <span class="stat-number">${results.length}</span>
                        <span class="stat-label">عدد الملفات</span>
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>عدد الأسطر</th>
                            <th>عدد الكلمات</th>
                            <th>عدد الأحرف</th>
                            <th>الأسطر الفارغة</th>
                            <th>متوسط الكلمات/السطر</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map(result => `
                            <tr>
                                <td><strong>${result.filename}</strong></td>
                                <td>${Utils.formatNumber(result.lines)}</td>
                                <td>${Utils.formatNumber(result.words)}</td>
                                <td>${Utils.formatNumber(result.characters)}</td>
                                <td>${Utils.formatNumber(result.emptyLines)}</td>
                                <td>${result.lines > 0 ? Math.round(result.words / result.lines * 100) / 100 : 0}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        resultsContent.innerHTML = tableHtml;
        this.showResultsSection();
    }

    // دمج الملفات
    async mergeFiles(options = {}) {
        await this.readFiles();
        
        const {
            separator = '\n\n--- الملف التالي ---\n\n',
            includeFilenames = true,
            addTimestamp = false
        } = options;

        let mergedContent = '';
        
        if (addTimestamp) {
            mergedContent += `تم الدمج في: ${Utils.formatDate(new Date())}\n\n`;
        }

        for (const fileObj of this.files) {
            if (includeFilenames) {
                mergedContent += `=== ${fileObj.name} ===\n\n`;
            }
            
            mergedContent += fileObj.content + separator;
        }

        // إزالة الفاصل الأخير
        mergedContent = mergedContent.slice(0, -separator.length);

        this.displayMergedResult(mergedContent);
    }

    // عرض نتيجة الدمج
    displayMergedResult(content) {
        const resultsContent = document.getElementById('resultsContent');
        
        resultsContent.innerHTML = `
            <div class="merged-content">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>الملف المدموج</h5>
                    <div>
                        <button class="btn btn-primary btn-sm" onclick="fileProcessor.downloadMergedFile()">
                            <i class="bi bi-download"></i> تحميل
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="Utils.copyToClipboard(this.getAttribute('data-content'))" data-content="${Utils.escapeHtml(content)}">
                            <i class="bi bi-clipboard"></i> نسخ
                        </button>
                    </div>
                </div>
                <div class="file-preview" style="max-height: 500px;">
                    ${content.split('\n').map((line, index) => 
                        `<div><span class="line-number">${index + 1}</span><span class="line-content">${Utils.escapeHtml(line)}</span></div>`
                    ).join('')}
                </div>
            </div>
        `;

        this.mergedContent = content;
        this.showResultsSection();
    }

    // تحميل الملف المدموج
    downloadMergedFile() {
        if (this.mergedContent) {
            const filename = `merged_files_${new Date().getTime()}.txt`;
            Utils.downloadFile(this.mergedContent, filename, 'text/plain');
        }
    }

    // استبدال النصوص
    async replaceText(options) {
        await this.readFiles();
        
        const {
            searchText,
            replaceWith,
            caseSensitive = false,
            wholeWord = false,
            useRegex = false
        } = options;

        const results = [];

        for (const fileObj of this.files) {
            let content = fileObj.content;
            let replacements = 0;

            try {
                if (useRegex) {
                    const flags = caseSensitive ? 'g' : 'gi';
                    const regex = new RegExp(searchText, flags);
                    const matches = content.match(regex);
                    replacements = matches ? matches.length : 0;
                    content = content.replace(regex, replaceWith);
                } else {
                    let searchPattern = searchText;
                    if (!caseSensitive) {
                        const regex = new RegExp(Utils.escapeRegex(searchText), 'gi');
                        const matches = content.match(regex);
                        replacements = matches ? matches.length : 0;
                        content = content.replace(regex, replaceWith);
                    } else {
                        const originalLength = content.length;
                        content = content.split(searchText).join(replaceWith);
                        replacements = Math.floor((originalLength - content.length) / (searchText.length - replaceWith.length));
                    }
                }

                results.push({
                    filename: fileObj.name,
                    success: true,
                    replacements: replacements,
                    newContent: content
                });

            } catch (error) {
                results.push({
                    filename: fileObj.name,
                    success: false,
                    error: error.message
                });
            }
        }

        this.displayReplaceResults(results);
    }

    // عرض نتائج الاستبدال
    displayReplaceResults(results) {
        const resultsContent = document.getElementById('resultsContent');
        
        const totalReplacements = results.reduce((sum, r) => sum + (r.replacements || 0), 0);

        const tableHtml = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                تم إجراء ${Utils.formatNumber(totalReplacements)} استبدال في ${results.filter(r => r.success).length} ملف
            </div>
            
            <div class="table-responsive">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>عدد الاستبدالات</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map((result, index) => `
                            <tr>
                                <td><strong>${result.filename}</strong></td>
                                <td>${result.success ? Utils.formatNumber(result.replacements) : '-'}</td>
                                <td>
                                    ${result.success ? 
                                        '<span class="badge badge-success">نجح</span>' : 
                                        '<span class="badge badge-danger">فشل</span>'
                                    }
                                </td>
                                <td>
                                    ${result.success ? 
                                        `<button class="btn btn-sm btn-primary" onclick="fileProcessor.downloadProcessedFile(${index})">
                                            <i class="bi bi-download"></i> تحميل
                                        </button>` : 
                                        `<span class="text-danger">${result.error}</span>`
                                    }
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        resultsContent.innerHTML = tableHtml;
        this.processedResults = results;
        this.showResultsSection();
    }

    // تحميل ملف معالج
    downloadProcessedFile(index) {
        const result = this.processedResults[index];
        if (result && result.success && result.newContent) {
            const filename = `processed_${result.filename}`;
            Utils.downloadFile(result.newContent, filename, 'text/plain');
        }
    }

    // إظهار قسم التقدم
    showProgressSection() {
        document.getElementById('progressSection').style.display = 'block';
        this.processingCancelled = false;
        this.processingPaused = false;
    }

    // إخفاء قسم التقدم
    hideProgressSection() {
        document.getElementById('progressSection').style.display = 'none';
    }

    // إظهار قسم النتائج
    showResultsSection() {
        document.getElementById('resultsSection').style.display = 'block';
    }

    // إيقاف المعالجة مؤقتاً
    pauseProcessing() {
        this.processingPaused = true;
        Utils.showAlert('تم إيقاف المعالجة مؤقتاً', 'warning');
    }

    // استئناف المعالجة
    resumeProcessing() {
        this.processingPaused = false;
        Utils.showAlert('تم استئناف المعالجة', 'info');
    }

    // إلغاء المعالجة
    cancelProcessing() {
        this.processingCancelled = true;
        this.hideProgressSection();
        Utils.showAlert('تم إلغاء المعالجة', 'danger');
    }
}

// إنشاء مثيل عام من معالج الملفات
const fileProcessor = new FileProcessor();
